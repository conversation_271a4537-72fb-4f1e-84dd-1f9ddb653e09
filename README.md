# Meteora Fees Checker - Season 2

🎯 **简单高效的Meteora第二季费用查询工具**

查询用户在Meteora第二季（2025年7月1日后）的累计已领取流动性提供费用。

## ✨ 功能特点

- 📊 **精确统计**: 统计2025-07-01后的已领取费用（fees，不含rewards）
- 🚀 **实时数据**: 直接调用Meteora官方API获取最新费用信息
- 🔧 **Dune SDK**: 使用官方Dune SDK简化数据拉取流程
- 🌐 **静态部署**: 无需服务器，支持GitHub Pages等静态托管
- 📱 **响应式设计**: 完美适配桌面和移动设备
- ⚡ **高性能**: 智能分片技术，支持大规模数据

## 🏗️ 项目架构

```
meteora-fees-checker/
├── data_fetcher.py          # 数据拉取脚本（从Dune API）
├── data_processor.py        # 数据处理脚本（生成索引文件）
├── index.html              # 前端查询页面
├── config.json             # 配置文件
├── requirements.txt        # Python依赖
├── data/                   # 原始数据存储
│   ├── new_lb_pairs.jsonl
│   └── new_positions.jsonl
└── web/                    # 部署文件
    ├── index.html
    ├── wallet_index.json
    └── stats.json
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd meteora-fees-checker

# 安装Python依赖（包含Dune SDK）
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.json` 文件：

```json
{
  "dune_api_key": "YOUR_DUNE_API_KEY_HERE",
  "queries": {
    "new_lb_pairs": "YOUR_LB_PAIRS_QUERY_ID",
    "new_positions": "YOUR_POSITIONS_QUERY_ID"
  }
}
```

**Dune查询要求:**

- **新lbPair查询** 应返回字段：`wallet`, `lbPair`
- **新position查询** 应返回字段：`wallet`, `position`
- 查询已经过优化，使用 `evt_block_date = date'2025-07-01'` 进行精确过滤
- 查询无需参数，直接使用 `dune.get_latest_result(query_id)` 获取结果

### 3. 数据拉取与处理

```bash
# 1. 从Dune API拉取数据（使用官方SDK）
python data_fetcher.py

# 2. 处理数据生成前端文件
python data_processor.py
```

### 4. 部署

将 `web/` 目录下的所有文件部署到静态托管平台：

**GitHub Pages:**
```bash
# 复制web目录内容到gh-pages分支
cp -r web/* docs/
git add docs/
git commit -m "Update data"
git push
```

**其他平台:**
- Netlify: 直接拖拽 `web/` 文件夹
- Vercel: 连接仓库并设置构建目录为 `web/`

## 📊 数据统计

### 支持的数据类型

1. **新lbPair**: 2025年7月1日后创建的流动性池
2. **新position**: 2025年7月1日后在已存在lbPair上创建的头寸

### API端点

- **lbPair费用**: `GET https://dlmm-api.meteora.ag/wallet/{wallet}/{lbPair}/earning`
- **position费用**: `GET https://dlmm-api.meteora.ag/position_v2/{position}`

## 🔧 高级配置

### Dune SDK优势

使用官方Dune SDK带来以下好处：

- **简化代码**: 无需手动处理API请求和响应
- **自动重试**: SDK内置智能重试机制
- **类型安全**: 提供完整的类型提示和文档
- **错误处理**: 更好的异常处理和调试信息
- **最新功能**: 及时获得Dune平台的最新特性

```python
# 简单的SDK调用示例
from dune_client import DuneClient

dune = DuneClient(api_key="your_api_key")
results = dune.get_latest_result(query_id)
```

### 实际数据结构

项目使用的真实Dune查询已经过优化，字段结构如下：

**新lbPair数据:**
```json
{
  "wallet": "钱包地址",
  "lbPair": "流动性池地址"
}
```

**新position数据:**
```json
{
  "wallet": "钱包地址", 
  "position": "头寸ID"
}
```

**查询逻辑说明:**
- **新lbPair**: 在2025年7月1日当天创建的流动性池
- **新position**: 在2025年7月1日当天在老流动性池上创建的新头寸
- 使用 `evt_block_date = date'2025-07-01'` 进行精确日期过滤
- 基于 `meteora_solana.lb_clmm_evt_positioncreate` 表查询

### 自定义API设置

```json
{
  "api_settings": {
    "meteora_base_url": "https://dlmm-api.meteora.ag",
    "request_timeout": 30,
    "max_retries": 3,
    "retry_delay": 1
  }
}
```

### 数据处理选项

```json
{
  "data_settings": {
    "batch_size": 1000,
    "max_file_size_mb": 90
  }
}
```

## 📝 使用说明

### 用户端操作

1. 访问部署的网页
2. 输入钱包地址
3. 点击"查询费用"
4. 查看总费用和详细信息

### 管理员操作

```bash
# 定期更新数据（建议每日执行）
python data_fetcher.py && python data_processor.py

# 查看数据统计
python -c "
import json
with open('web/stats.json') as f:
    stats = json.load(f)
    print(f'总钱包数: {stats[\"summary\"][\"totalWallets\"]}')
    print(f'总lbPair数: {stats[\"summary\"][\"totalLbPairs\"]}')
    print(f'总position数: {stats[\"summary\"][\"totalPositions\"]}')
"
```

## 🔍 故障排除

### 常见问题

**Q: Dune API调用失败**
```
A: 检查API密钥是否正确，查询ID是否有效
```

**Q: 文件过大无法部署**
```
A: 数据处理器会自动分片，确保单文件小于90MB
```

**Q: 前端无法加载数据**
```
A: 检查JSON文件是否正确生成，CORS设置是否正确
```

### 调试方法

```bash
# 检查数据文件
ls -la data/
ls -la web/

# 验证JSON格式
python -m json.tool web/wallet_index.json > /dev/null && echo "JSON格式正确"

# 测试API连接
curl "https://dlmm-api.meteora.ag/position_v2/TEST_POSITION_ID"
```

## 📊 性能优化

### 数据分片

当钱包数据超过90MB时，系统自动启用分片：

- 按钱包地址首字符分片（0-9, a-f）
- 主索引文件记录分片映射
- 前端按需加载分片数据

### 缓存策略

- 原始数据增量更新，避免重复拉取
- 前端使用浏览器缓存优化加载速度
- 建议设置CDN缓存提升全球访问速度

## 🛡️ 安全考虑

- 所有API调用在前端执行，无需后端
- 不存储用户私钥或敏感信息
- 仅读取公开的区块链数据

## 📈 扩展功能

### 计划中的功能

- [ ] 价格转换API集成
- [ ] 历史费用趋势图表
- [ ] 导出CSV/PDF报告
- [ ] 多语言支持

### 自定义开发

可以通过修改以下文件进行扩展：

- `data_fetcher.py`: 添加新的数据源
- `data_processor.py`: 修改数据处理逻辑
- `index.html`: 自定义前端界面

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 💬 支持

如有问题或建议，请：

1. 查看 [Issues](../../issues) 寻找解决方案
2. 创建新的 Issue 描述问题
3. 参考 [Wiki](../../wiki) 获取更多文档

---

**📌 重要提醒**: 此工具仅用于查询和统计，不构成投资建议。请在使用前确保理解相关风险。