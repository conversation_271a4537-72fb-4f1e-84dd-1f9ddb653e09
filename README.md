# Meteora Fees Checker - Season 2 部署指南

## 项目概述

本项目是一个用于查询 Meteora Season 2 已领取费用的 Web 工具，采用了**革命性的滑动窗口 + 布隆过滤器架构**，彻底解决了数据无限增长和查询效率问题。

## 核心特性

- **🔧 滑动窗口**: 只保留最近30天活跃钱包，自动清理过期数据
- **⚡ 布隆过滤器**: 几KB大小实现毫秒级存在性检查，99%+准确率
- **📊 紧凑存储**: 高度优化的数据结构，最大80MB总大小限制
- **🔄 智能清理**: 自动移除过期和不活跃钱包，防止数据爆炸
- **📈 数量控制**: 最大50,000个钱包限制，超出时移除最老数据

## 文件结构

```
meteora-fees-checker/
├── index.html                    # Web 界面主文件
├── data_fetcher.py              # 优化的数据获取脚本
├── dune_queries.sql             # Dune 查询 SQL
├── .github/
│   └── workflows/
│       └── update-data.yml      # GitHub Actions 工作流
├── data/                        # 数据存储目录
│   ├── bloom_filter.json        # 布隆过滤器（几KB）
│   ├── wallet_index.json        # 钱包索引映射
│   ├── compact_data.json        # 紧凑的关系数据
│   ├── metadata.json            # 元数据和统计
│   └── web/                     # Web 优化数据
│       ├── query_api.json       # 查询API（含布隆过滤器）
│       ├── wallet_index.json    # Web钱包索引
│       └── wallet_data.json     # Web关系数据
├── requirements.txt             # Python 依赖
└── README.md                   # 项目说明
```

## 数据存储架构

### 1. 滑动窗口机制
- **时间窗口**: 保留最近30天的活跃钱包
- **数量限制**: 最大50,000个钱包
- **自动清理**: 超出时移除最老的钱包数据
- **增量更新**: 只更新新增和变化的钱包

### 2. 布隆过滤器优化
```json
{
  "size": 100000,
  "hash_count": 5,
  "bit_array": [0,1,0,1,1,0,...]  // 仅几KB大小
}
```

### 3. 紧凑数据结构
```json
{
  "lbpair": {
    "wallet1": ["lbpair1", "lbpair2"],
    "wallet2": ["lbpair3"]
  },
  "position": {
    "wallet1": ["position1", "position2"],
    "wallet3": ["position3"]
  }
}
```

### 4. 智能索引
```json
{
  "wallet1": {
    "last_seen": "2025-08-09T12:00:00Z",
    "has_lbpair": true,
    "has_position": true
  }
}
```

## 查询流程优化

1. **布隆过滤器检查** → 毫秒级判断钱包是否可能存在 (99%+准确率)
2. **精确索引查询** → O(1) 确认钱包确实存在
3. **数据获取** → 从紧凑数据中获取关系
4. **API调用** → 调用 Meteora API 计算实时费用

## 性能指标

| 特性 | 传统方案 | 新方案 |
|------|----------|--------|
| 文件大小 | 无限增长 → 超限 | 固定80MB上限 |
| 查询时间 | O(n) 遍历查找 | O(1) 布隆过滤器 |
| 内存占用 | 加载全部数据 | 按需加载紧凑数据 |
| 数据保留 | 永久存储 | 30天滑动窗口 |
| 误判率 | 0% | <1% (布隆过滤器) |

## 1. 环境准备

### 1.1 获取 Dune API Key

1. 访问 [Dune Analytics](https://dune.com/)
2. 注册/登录账号
3. 进入 Settings → API Keys
4. 创建新的 API Key

### 1.2 创建 Dune 查询

在 Dune 平台创建以下查询：

1. **Query 1**: wallet-lbPair 关系（7.1后创建的 lbPair）
2. **Query 2**: wallet-position 关系（7.1前创建的 lbPair，但position在7.1后）

**重要**: 日期范围控制完全在 SQL 中处理，无需在脚本中传递日期参数。

## 2. 项目配置

### 2.1 Python 依赖

创建 `requirements.txt`：

```txt
dune-client>=1.5.0
requests>=2.31.0
```

### 2.2 环境变量

- `DUNE_API_KEY`: Dune API Key
- `WALLET_LBPAIR_QUERY_ID`: wallet-lbPair 查询 ID
- `WALLET_POSITION_QUERY_ID`: wallet-position 查询 ID

## 3. GitHub 部署

### 3.1 配置 GitHub Secrets

在仓库设置中添加以下 Secrets：

- `DUNE_API_KEY`: Dune API Key
- `WALLET_LBPAIR_QUERY_ID`: wallet-lbPair 查询 ID（数字）
- `WALLET_POSITION_QUERY_ID`: wallet-position 查询 ID（数字）

### 3.2 GitHub Actions 工作流

创建 `.github/workflows/update-data.yml`：

```yaml
name: Update Meteora Data

on:
  schedule:
    # 每天 UTC 02:00 执行
    - cron: '0 2 * * *'
  workflow_dispatch:
    # 支持手动触发

jobs:
  update-data:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run optimized data fetcher
      run: python data_fetcher.py
      env:
        DUNE_API_KEY: ${{ secrets.DUNE_API_KEY }}
        WALLET_LBPAIR_QUERY_ID: ${{ secrets.WALLET_LBPAIR_QUERY_ID }}
        WALLET_POSITION_QUERY_ID: ${{ secrets.WALLET_POSITION_QUERY_ID }}
    
    - name: Commit and push changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add data/
        if git diff --staged --quiet; then
          echo "No data changes to commit"
        else
          git commit -m "Update optimized data: $(date +'%Y-%m-%d %H:%M:%S UTC')"
          git push
        fi
```

### 3.3 启用 GitHub Pages

1. 进入仓库 Settings → Pages
2. Source 选择 "Deploy from a branch"
3. Branch 选择 "main"
4. Path 选择 "/ (root)"
5. 保存配置

## 4. 本地开发和测试

### 4.1 本地运行数据获取

```bash
# 设置环境变量
export DUNE_API_KEY="your_dune_api_key"
export WALLET_LBPAIR_QUERY_ID="123456"
export WALLET_POSITION_QUERY_ID="789012"

# 安装依赖
pip install -r requirements.txt

# 运行优化的数据获取
python data_fetcher.py
```

### 4.2 本地测试 Web 界面

```bash
# 启动本地服务器
python -m http.server 8000

# 访问 http://localhost:8000
```

## 5. 数据管理策略

### 5.1 自动数据清理

- **时间清理**: 自动删除30天前的钱包数据
- **数量清理**: 超过50,000个钱包时移除最老的
- **大小控制**: 总文件大小限制在80MB以内
- **布隆重建**: 清理后自动重建布隆过滤器

### 5.2 数据保留策略

```python
# 配置参数
retention_days = 30      # 数据保留天数
max_wallets = 50000     # 最大钱包数量
max_total_size_mb = 80  # 总文件大小限制
```

### 5.3 性能监控

- 布隆过滤器效率监控
- 文件大小实时跟踪
- 查询性能统计
- 误判率分析

## 6. 查询优化

### 6.1 三层查询架构

1. **Layer 1**: 布隆过滤器快速过滤（毫秒级）
2. **Layer 2**: 精确索引确认（O(1)查找）
3. **Layer 3**: 紧凑数据获取（最小IO）

### 6.2 缓存策略

- 布隆过滤器常驻内存
- 索引数据智能缓存
- 关系数据按需加载

## 7. 监控和维护

### 7.1 关键指标监控

- **数据新鲜度**: 最后更新时间
- **覆盖率**: 活跃钱包覆盖程度  
- **效率**: 布隆过滤器命中率
- **容量**: 文件大小使用率

### 7.2 自动化维护

- 每日数据清理
- 布隆过滤器优化
- 索引完整性检查
- 性能基准测试

## 8. 故障排除

### Q1: 布隆过滤器误判

**问题**: 钱包存在但查询显示不存在
**解决**: 重新构建布隆过滤器，调整哈希函数数量

### Q2: 数据大小超限

**问题**: 文件大小超过80MB限制
**解决**: 减少retention_days或max_wallets参数

### Q3: 查询性能下降

**问题**: 查询响应时间增加
**解决**: 检查数据结构完整性，重建索引

## 9. 扩展和优化

### 9.1 进一步优化

- **Cuckoo过滤器**: 支持删除操作的过滤器
- **压缩算法**: 使用Gzip压缩紧凑数据
- **缓存预热**: 预加载热门钱包数据

### 9.2 扩展功能

- **批量查询**: 支持多钱包同时查询
- **历史追踪**: 钱包活动历史记录
- **统计分析**: 费用分布和趋势分析

## 10. 部署检查清单

- [ ] 创建 Dune 查询并记录 Query ID
- [ ] 设置 GitHub Secrets
- [ ] 上传项目文件
- [ ] 启用 GitHub Pages
- [ ] 测试 GitHub Actions 工作流
- [ ] 验证布隆过滤器生成
- [ ] 测试滑动窗口清理
- [ ] 验证 Web 界面查询功能
- [ ] 检查文件大小限制
- [ ] 验证查询性能

---

## 技术亮点

✅ **恒定大小**: 文件大小永不超过80MB
✅ **毫秒查询**: 布隆过滤器实现亚秒级响应
✅ **自动清理**: 滑动窗口防止数据爆炸
✅ **高效存储**: 紧凑数据结构最小化空间
✅ **智能优化**: 多层架构平衡速度和准确性
✅ **GitHub完美**: 专为GitHub Pages优化设计

## 联系方式

如有问题，请提交 GitHub Issue 或联系项目维护者。