我想要做一个 meteora fees 检查器具，主要针对的是第二季的活动，即用户在 2025.07.01 及以后通过 meteora 上的 Lp 已领取的费用。

## 统计口径
- 口径：以 2025-07-01 00:00:00 UTC 及以后发生的已领取费用（claimed）为准
- 范围：仅统计 fee，不含 reward
- 单位：以 USD 输出
- 时区与窗口：采用 UTC，统计窗口为 [2025-07-01 00:00:00 UTC, 活动结束]
- 价格口径：按事件发生时点价格进行换算（Dune 上的 Pyth/Jupiter 等价格源）；若该时点无可用价格，则回退到相邻时刻价格；仍失败则记录为异常并不计入


当前可以使用的平台：
dune 平台可以获取 wallet 和 lbPair 的对应关系
也可以获取 wallet 与 position 的对应关系

## 数据来源与用途
- Dune：作为主数据源，提取 2025-07-01 之后的 claim/collect 事件，并获取事件时点价格用于 USD 换算；同时用于发现 wallet↔position↔lbPair 的对应关系
- 官方 API：
  - /wallet/{wallet}/{lbPair}/earning：本方案不用于期间统计（该接口返回累计 claimed，不支持时间切片）
  - /position_v2/{position}：仅用于元数据补充（如 position 对应的 lbPair、代币信息），不用于时间切片计算


当前可用的几个 meteora 官方接口：
1.获取某个地址在某个 lbPair 上累计已领取（claimed）的全部 fees（不支持时间切片）
curl -X 'GET' \
  'https://dlmm-api.meteora.ag/wallet/{wallet}/{lbPair}/earning' \
  -H 'accept: application/json'

返回结果:
{
  "total_fee_x_claimed": "0",
  "total_fee_y_claimed": "0",
  "total_reward_x_claimed": "0",
  "total_reward_y_claimed": "0",
  "total_fee_usd_claimed": 0,
  "total_reward_usd_claimed": 0
}
该接口不用于期间统计（为累计 claimed，不支持时间切片）；其中 total_fee_usd_claimed 字段以 USD 计价，供参考。

2. 根据 position 计算该position获得的 fees
curl -X 'GET' \
  'https://dlmm-api.meteora.ag/position_v2/{position}' \
  -H 'accept: application/json'


1. 使用 Dune 提取 2025-07-01 之后的 claim/collect 事件，按事件发生时点价格换算 USD，并聚合到 position→lbPair→wallet 维度；
2. 通过 Dune 获取 wallet↔position↔lbPair 的对应关系；/position_v2/{position} 仅作元数据补充；
3. /wallet/{wallet}/{lbPair}/earning 不参与期间统计；对于“7/1 前创建、7/1 后加仓”的老 position，由于以 claim 事件为准，天然仅计入 7/1 后发生的领取，无需用接口2做时间拆分。

其他说明：
1. 以“7/1 之后的领取事件（claimed）为准”，仅统计 fee（不含 reward）
2. 使用 UTC 边界：2025-07-01 00:00:00 UTC。
3. 单位为 USD，按事件发生时点价格换算


最终的展现形式：
1. 通过 github 部署好的网页，用户输入自己的钱包地址，就可以计算出7.1 累计到当天的fees
2. 数据获取：wallet 与 position 以及 lbPair 的对应关系，通过 dune 的接口按天来获取，存在本地文件中
3. fees 计算，所有钱包截止到当前时间所有的积分，以及参与过的所有的 lbPair 信息。
4. 用户通过 1 提供的网站，输入自己的钱包，就能快速获取 S2 的积分总和