我想要做一个 meteora fees 检查器具，主要针对的是第二季的活动，即用户在 2025.07.01 及以后通过 meteora 上的 Lp 已领取的费用。

当前可以使用的平台：
dune 平台可以获取 wallet 和 lbPair 的对应关系
也可以获取 wallet 与 position 的对应关系

当前可用的几个 meteora 官方接口：
1.获取某个地址在某个 lbPair 上赚取的全部的 fees
curl -X 'GET' \
  'https://dlmm-api.meteora.ag/wallet/{wallet}/{lbPair}/earning' \
  -H 'accept: application/json'

返回结果:
{
  "total_fee_x_claimed": "0",
  "total_fee_y_claimed": "0",
  "total_reward_x_claimed": "0",
  "total_reward_y_claimed": "0",
  "total_fee_usd_claimed": 0,
  "total_reward_usd_claimed": 0
}
其中 total_fee_usd_claimed 是唯一需要关注的字段。

2. 根据 position 计算该position获得的 fees
curl -X 'GET' \
  'https://dlmm-api.meteora.ag/position_v2/{position}' \
  -H 'accept: application/json'


1. 针对当前可用的接口，我的思路是，获取 2025.07.01 及以后，用户与 lbPair 的对应关系，然后通过上述接口获取用户全部的fees;
2. 如果用户在 2025.07.01 及以后继续在07.01之前创建好的 lbPair 上添加流动性，这部分数据，就需要通过接口2来实现，因为接口1只能获取到用户在某个 lbPair 上赚取的全部的 fees，无法区分出是哪个时间段的 fees。

其他说明：
1. 以“7/1 之后的领取事件（claimed）为准”，仅统计 fee（不含 reward）
2. 使用 UTC 边界：2025-07-01 00:00:00 UTC。
3. 单位是 usd，接口可以直接获取，bu yo
4. 