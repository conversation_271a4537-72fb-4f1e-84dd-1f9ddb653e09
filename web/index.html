<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Checker - Season 2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #718096;
            font-size: 1.1rem;
        }

        .header .season-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 0;
        }

        .search-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 120px;
        }

        .search-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
        }

        .results-section.show {
            display: block;
        }

        .summary-card {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .summary-card h2 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .summary-card .total-amount {
            font-size: 3rem;
            font-weight: 700;
            margin: 15px 0;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
        }

        .stat-item .label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stat-item .value {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 5px;
        }

        .details-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }

        .items-grid {
            display: grid;
            gap: 15px;
        }

        .item-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s;
        }

        .item-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .item-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .item-type {
            background: #e2e8f0;
            color: #2d3748;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .item-type.lb-pair {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .item-type.position {
            background: #c6f6d5;
            color: #276749;
        }

        .item-amount {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2d3748;
        }

        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .detail-label {
            color: #718096;
            font-size: 0.9rem;
        }

        .detail-value {
            color: #2d3748;
            font-weight: 600;
            font-size: 0.9rem;
            font-family: monospace;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .info-message {
            background: #bee3f8;
            color: #2b6cb0;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .search-form {
                flex-direction: column;
            }

            .summary-card .total-amount {
                font-size: 2.5rem;
            }

            .summary-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💫 Meteora Fees Checker</h1>
            <div class="season-badge">Season 2 - 2025.07.01+</div>
            <p class="subtitle">查询您在Meteora第二季的累计已领取费用</p>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="walletAddress">钱包地址</label>
                    <input
                        type="text"
                        id="walletAddress"
                        placeholder="输入您的钱包地址..."
                        required
                    >
                </div>
                <button type="submit" class="search-btn" id="searchBtn">
                    查询费用
                </button>
            </form>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在查询费用数据，请稍候...</p>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="summary-card">
                <h2>总计已领取费用</h2>
                <div class="total-amount" id="totalAmount">$0.00</div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="label">新流动性池</div>
                        <div class="value" id="lbPairCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="label">新头寸</div>
                        <div class="value" id="positionCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="label">总项目数</div>
                        <div class="value" id="totalItems">0</div>
                    </div>
                </div>
            </div>

            <div class="details-section">
                <div class="section-title">📊 详细信息</div>
                <div class="items-grid" id="itemsGrid"></div>
            </div>
        </div>

        <div class="info-message">
            <strong>说明：</strong>
            此工具统计2025年7月1日以后创建的流动性池和头寸的已领取费用。
            数据通过Meteora官方API实时获取，确保准确性。
        </div>

        <div class="footer">
            <p>Meteora Fees Checker | 数据更新时间: <span id="lastUpdated">-</span></p>
        </div>
    </div>

    <script>
        class MeteoraFeesChecker {
            constructor() {
                this.routerIndex = null;
                this.shardCache = new Map(); // 缓存已加载的分片
                this.init();
            }

            async init() {
                try {
                    await this.loadRouterIndex();
                    this.bindEvents();
                } catch (error) {
                    this.showError('初始化失败: ' + error.message);
                }
            }

            async loadRouterIndex() {
                try {
                    const response = await fetch('./wallet_index.json');
                    if (!response.ok) throw new Error('无法加载路由索引');

                    this.routerIndex = await response.json();

                    // 更新页面信息
                    if (this.routerIndex.lastUpdated) {
                        document.getElementById('lastUpdated').textContent =
                            new Date(this.routerIndex.lastUpdated).toLocaleString('zh-CN');
                    }

                } catch (error) {
                    throw new Error('加载路由索引失败: ' + error.message);
                }
            }

            getShardKey(walletAddress) {
                // 根据Solana钱包地址计算分片键
                const cleanAddress = walletAddress;
                const prefixLength = this.routerIndex.shardPrefixLength || 2;
                return cleanAddress.substring(0, prefixLength).padEnd(prefixLength, '0');
            }

            async loadShardData(shardKey) {
                try {
                    // 检查缓存
                    if (this.shardCache.has(shardKey)) {
                        return this.shardCache.get(shardKey);
                    }

                    // 检查分片是否存在
                    const shardInfo = this.routerIndex.shards[shardKey];
                    if (!shardInfo) {
                        return null;
                    }

                    // 加载分片文件
                    const response = await fetch(`./shards/${shardInfo.filename}`);
                    if (!response.ok) throw new Error('无法加载分片数据');

                    const shardData = await response.json();
                    const wallets = shardData.wallets || {};

                    // 缓存分片数据
                    this.shardCache.set(shardKey, wallets);

                    return wallets;

                } catch (error) {
                    console.error('加载分片数据失败:', error);
                    return null;
                }
            }

            bindEvents() {
                document.getElementById('searchForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.searchWallet();
                });
            }

            async searchWallet() {
                const walletAddress = document.getElementById('walletAddress').value.trim();

                if (!walletAddress) {
                    this.showError('请输入钱包地址');
                    return;
                }

                this.showLoading(true);
                this.hideError();
                this.hideResults();

                try {
                    const walletInfo = await this.getWalletInfo(walletAddress);

                    if (!walletInfo || (!walletInfo.newLbPairs.length && !walletInfo.newPositions.length)) {
                        this.showError('未找到该钱包在第二季的相关数据');
                        return;
                    }

                    await this.fetchAndDisplayFees(walletAddress, walletInfo);

                } catch (error) {
                    this.showError('查询过程中出错: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async getWalletInfo(walletAddress) {
                // 计算分片键
                const shardKey = this.getShardKey(walletAddress);

                // 加载对应分片
                const shardWallets = await this.loadShardData(shardKey);

                return shardWallets ? shardWallets[walletAddress] : null;
            }

            async fetchAndDisplayFees(walletAddress, walletInfo) {
                const results = {
                    lbPairs: [],
                    positions: [],
                    totalFees: 0
                };

                // 获取lbPair费用
                for (const lbPairInfo of walletInfo.newLbPairs) {
                    try {
                        const fees = await this.fetchLbPairFees(walletAddress, lbPairInfo.lbPair);
                        if (fees) {
                            results.lbPairs.push({
                                ...lbPairInfo,
                                fees: fees
                            });
                            results.totalFees += fees.totalFeesUsd || 0;
                        }
                    } catch (error) {
                        console.error(`获取lbPair ${lbPairInfo.lbPair} 费用失败:`, error);
                    }
                }

                // 获取position费用
                for (const positionInfo of walletInfo.newPositions) {
                    try {
                        const fees = await this.fetchPositionFees(positionInfo.position);
                        if (fees) {
                            results.positions.push({
                                ...positionInfo,
                                fees: fees
                            });
                            results.totalFees += fees.totalFeesUsd || 0;
                        }
                    } catch (error) {
                        console.error(`获取position ${positionInfo.position} 费用失败:`, error);
                    }
                }

                this.displayResults(results);
            }

            async fetchLbPairFees(wallet, lbPair) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/wallet/${wallet}/${lbPair}/earning`
                    );

                    if (!response.ok) return null;

                    const data = await response.json();

                    // 计算总费用（只统计fees，不含rewards）
                    let totalFeesUsd = 0;
                    if (data.fees) {
                        for (const [token, amount] of Object.entries(data.fees)) {
                            // 这里需要转换为USD，实际实现时需要获取价格
                            // 暂时假设API返回的已经是USD值
                            totalFeesUsd += parseFloat(amount) || 0;
                        }
                    }

                    return {
                        ...data,
                        totalFeesUsd: totalFeesUsd
                    };

                } catch (error) {
                    console.error('获取lbPair费用失败:', error);
                    return null;
                }
            }

            async fetchPositionFees(position) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/position/${position}/claim_fees`
                    );

                    if (!response.ok) return null;

                    const claimRecords = await response.json();

                    // 计算所有claim记录的总fees
                    let totalFeesUsd = 0;
                    if (Array.isArray(claimRecords)) {
                        for (const record of claimRecords) {
                            const tokenXUsd = parseFloat(record.token_x_usd_amount) || 0;
                            const tokenYUsd = parseFloat(record.token_y_usd_amount) || 0;
                            totalFeesUsd += tokenXUsd + tokenYUsd;
                        }
                    }

                    return {
                        claimRecords: claimRecords,
                        totalFeesUsd: totalFeesUsd
                    };

                } catch (error) {
                    console.error('获取position费用失败:', error);
                    return null;
                }
            }

            displayResults(results) {
                // 更新汇总信息
                document.getElementById('totalAmount').textContent =
                    '$' + results.totalFees.toFixed(2);
                document.getElementById('lbPairCount').textContent = results.lbPairs.length;
                document.getElementById('positionCount').textContent = results.positions.length;
                document.getElementById('totalItems').textContent =
                    results.lbPairs.length + results.positions.length;

                // 生成详细列表
                const itemsGrid = document.getElementById('itemsGrid');
                itemsGrid.innerHTML = '';

                // 添加lbPair项目
                results.lbPairs.forEach(item => {
                    const card = this.createItemCard('lb-pair', item);
                    itemsGrid.appendChild(card);
                });

                // 添加position项目
                results.positions.forEach(item => {
                    const card = this.createItemCard('position', item);
                    itemsGrid.appendChild(card);
                });

                this.showResults();
            }

            createItemCard(type, item) {
                const card = document.createElement('div');
                card.className = 'item-card';

                const typeLabel = type === 'lb-pair' ? '流动性池' : '头寸';
                const identifier = type === 'lb-pair' ? item.lbPair : item.position;

                card.innerHTML = `
                    <div class="item-header">
                        <span class="item-type ${type}">${typeLabel}</span>
                        <span class="item-amount">${(item.fees?.totalFeesUsd || 0).toFixed(2)}</span>
                    </div>
                    <div class="item-details">
                        <div class="detail-item">
                            <span class="detail-label">${type === 'lb-pair' ? 'LB Pair' : 'Position'}:</span>
                            <span class="detail-value">${identifier.substring(0, 8)}...${identifier.substring(-8)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">费用类型:</span>
                            <span class="detail-value">${type === 'lb-pair' ? '流动性池费用' : '头寸费用'}</span>
                        </div>
                    </div>
                `;

                return card;
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                const searchBtn = document.getElementById('searchBtn');

                if (show) {
                    loading.classList.add('show');
                    searchBtn.disabled = true;
                    searchBtn.textContent = '查询中...';
                } else {
                    loading.classList.remove('show');
                    searchBtn.disabled = false;
                    searchBtn.textContent = '查询费用';
                }
            }

            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.classList.add('show');
            }

            hideError() {
                document.getElementById('errorMessage').classList.remove('show');
            }

            showResults() {
                document.getElementById('resultsSection').classList.add('show');
            }

            hideResults() {
                document.getElementById('resultsSection').classList.remove('show');
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new MeteoraFeesChecker();
        });
    </script>
</body>
</html>