<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><PERSON><PERSON><PERSON> Checker - Season 2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: containerFadeIn 0.8s ease-out;
        }

        @keyframes containerFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: headerSlideIn 1s ease-out 0.2s both;
        }

        @keyframes headerSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            color: #718096;
            font-size: 1.1rem;
            opacity: 0.8;
            animation: subtitleFadeIn 1s ease-out 0.4s both;
        }

        @keyframes subtitleFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 0.8;
                transform: translateY(0);
            }
        }

        .header .season-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 0;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .search-section {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            animation: searchSlideUp 0.8s ease-out 0.4s both;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        @keyframes searchSlideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .search-form {
            display: flex;
            gap: 20px;
            align-items: flex-end;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2d3748;
            font-size: 1rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .search-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            position: relative;
            overflow: hidden;
        }

        .search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .search-btn:hover::before {
            left: 100%;
        }

        .search-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 15px 25px rgba(102, 126, 234, 0.4);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            animation: loadingFadeIn 0.5s ease-out;
        }

        .loading.show {
            display: block;
        }

        @keyframes loadingFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
            animation: resultsSlideIn 0.8s ease-out;
        }

        .results-section.show {
            display: block;
        }

        @keyframes resultsSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .summary-card {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(76, 175, 80, 0.3);
            animation: summaryPop 0.6s ease-out 0.2s both;
        }

        @keyframes summaryPop {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .summary-card h2 {
            font-size: 2.2rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .summary-card .total-amount {
            font-size: 3.5rem;
            font-weight: 700;
            margin: 20px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            animation: amountCount 1s ease-out 0.8s both;
        }

        @keyframes amountCount {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .details-section {
            margin-top: 40px;
            animation: detailsFadeIn 0.8s ease-out 0.6s both;
        }

        @keyframes detailsFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin: 40px 0 25px 0;
            padding-bottom: 15px;
            border-bottom: 3px solid transparent;
            background: linear-gradient(90deg, #667eea, #764ba2) 0 100% / 100% 3px no-repeat;
            animation: titleSlideIn 0.8s ease-out;
        }

        @keyframes titleSlideIn {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .items-grid {
            display: grid;
            gap: 20px;
        }

        .item-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: none;
            border-radius: 16px;
            padding: 25px;
            transition: all 0.3s ease;
            animation: cardSlideIn 0.5s ease-out calc(var(--index) * 0.1s) both;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        @keyframes cardSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .item-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .item-type {
            background: linear-gradient(45deg, #e2e8f0, #cbd5e0);
            color: #2d3748;
            padding: 6px 15px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .item-type.lb-pair {
            background: linear-gradient(45deg, #bee3f8, #90cdf4);
            color: #2b6cb0;
        }

        .item-type.position {
            background: linear-gradient(45deg, #c6f6d5, #9ae6b4);
            color: #276749;
        }

        .item-type.pair {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }

        .item-amount {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            padding: 12px 10px;
            margin: 0 -10px;
        }

        .detail-label {
            color: #718096;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .detail-value {
            color: #2d3748;
            font-weight: 600;
            font-size: 0.9rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .pair-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 700;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            padding: 2px 0;
        }

        .pair-link:hover {
            color: #764ba2;
            border-bottom-color: #764ba2;
            text-shadow: 0 1px 2px rgba(118, 75, 162, 0.2);
            transform: translateY(-1px);
        }

        .pair-title-link {
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .pair-title-link:hover {
            color: #f0f8ff;
            border-bottom-color: rgba(255, 255, 255, 0.5);
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .pair-title {
            color: white;
            font-weight: 600;
        }

        .error-message, .info-message {
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            animation: messageSlideIn 0.5s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .error-message {
            background: linear-gradient(145deg, #fed7d7, #feb2b2);
            color: #c53030;
            border-left: 4px solid #e53e3e;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .info-message {
            background: linear-gradient(145deg, #bee3f8, #90cdf4);
            color: #2b6cb0;
            border-left: 4px solid #3182ce;
        }

        .contact-section {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            border: 1px solid #e2e8f0;
        }

        .contact-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .contact-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .contact-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .contact-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        .contact-icon {
            font-size: 1.1rem;
        }

        .donation-section {
            background: linear-gradient(135deg, #fef5e7, #fed7aa);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid #f59e0b;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .donation-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 10px;
        }

        .donation-text {
            color: #a16207;
            margin-bottom: 20px;
            font-size: 1rem;
        }

        .wallet-address {
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #f59e0b;
            max-width: 600px;
            margin: 0 auto;
        }

        .wallet-label {
            display: block;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .wallet-value {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: #fef3c7;
            padding: 12px;
            border-radius: 8px;
            word-break: break-all;
            font-size: 0.9rem;
            color: #92400e;
            border: 1px solid #fbbf24;
            margin-bottom: 15px;
            line-height: 1.5;
            overflow-wrap: break-word;
            hyphens: none;
        }

        .copy-btn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #d97706, #b45309);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .copy-success {
            background: linear-gradient(135deg, #10b981, #059669) !important;
        }

        .pagination-info {
            text-align: center;
            margin-bottom: 20px;
            color: #718096;
            font-size: 0.9rem;
        }

        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            color: #4a5568;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
            min-width: 40px;
            text-align: center;
        }

        .pagination-btn:hover:not(:disabled) {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        .pagination-btn.active:hover {
            transform: translateY(-1px);
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: 20px;
        }

        .page-size-selector select {
            padding: 6px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #4a5568;
            font-weight: 600;
            cursor: pointer;
        }

        .page-size-selector select:focus {
            outline: none;
            border-color: #667eea;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9rem;
            animation: footerFadeIn 1s ease-out 1s both;
        }

        @keyframes footerFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 5px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .header .subtitle {
                font-size: 1rem;
            }

            .search-form {
                flex-direction: column;
                gap: 10px;
            }

            .search-btn {
                width: 100%;
                padding: 15px;
            }

            .summary-card .total-amount {
                font-size: 2rem;
            }

            .items-grid {
                gap: 10px;
            }

            .item-card {
                padding: 15px;
            }

            .item-details {
                grid-template-columns: 1fr;
                gap: 5px;
            }

            .detail-value {
                font-size: 0.8rem;
                word-break: break-all;
            }

            .contact-links {
                flex-direction: column;
                gap: 15px;
            }

            .contact-link {
                padding: 12px 20px;
            }

            .pagination-controls {
                gap: 10px;
            }

            .pagination-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
                min-width: 35px;
            }

            .page-size-selector {
                margin-left: 0;
                margin-top: 10px;
                justify-content: center;
            }

            .donation-section {
                padding: 20px;
                margin: 15px 0;
            }

            .donation-title {
                font-size: 1.1rem;
            }

            .wallet-address {
                padding: 15px;
            }

            .wallet-value {
                font-size: 0.8rem;
                padding: 10px;
            }

            .copy-btn {
                padding: 8px 16px;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                margin: 2px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header .season-badge {
                font-size: 0.8rem;
                padding: 3px 10px;
            }

            .summary-card {
                padding: 20px;
            }

            .summary-card .total-amount {
                font-size: 1.8rem;
            }

            .form-group input {
                font-size: 16px;
                padding: 15px;
            }

            .search-btn {
                padding: 15px;
                font-size: 16px;
            }

            .item-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .item-amount {
                font-size: 1.2rem;
            }

            .section-title {
                font-size: 1.2rem;
            }

            .contact-section {
                padding: 15px;
                margin: 15px 0;
            }

            .contact-title {
                font-size: 1rem;
            }

            .contact-links {
                flex-direction: column;
                gap: 12px;
            }

            .contact-link {
                padding: 10px 16px;
                font-size: 0.9rem;
            }

            .donation-section {
                padding: 15px;
                margin: 15px 0;
            }

            .donation-title {
                font-size: 1rem;
            }

            .donation-text {
                font-size: 0.9rem;
            }

            .wallet-address {
                padding: 12px;
            }

            .wallet-value {
                font-size: 0.75rem;
                padding: 8px;
                line-height: 1.4;
            }

            .copy-btn {
                padding: 8px 14px;
                font-size: 0.8rem;
            }

            .pagination-controls {
                gap: 8px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-btn {
                padding: 6px 10px;
                font-size: 0.75rem;
                min-width: 30px;
            }

            .page-size-selector {
                width: 100%;
                margin-top: 15px;
                justify-content: center;
            }

            .page-size-selector select {
                padding: 8px 12px;
                font-size: 0.9rem;
            }

            .pagination-info {
                font-size: 0.8rem;
                margin-bottom: 15px;
            }
        }

        @media (max-width: 360px) {
            .container {
                padding: 8px;
                margin: 1px;
            }

            .header h1 {
                font-size: 1.3rem;
            }

            .wallet-value {
                font-size: 0.7rem;
                padding: 6px;
                word-break: break-all;
                line-height: 1.3;
            }

            .contact-link {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .pagination-btn {
                padding: 4px 8px;
                font-size: 0.7rem;
                min-width: 28px;
            }

            .item-details {
                grid-template-columns: 1fr;
            }

            .detail-value {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💫 Meteora Fees Checker</h1>
            <div class="season-badge">Season 2 - 2025.07.01+</div>
            <p class="subtitle">Check your claimed fees in Meteora Season 2</p>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="walletAddress">Wallet Address</label>
                    <input
                        type="text"
                        id="walletAddress"
                        placeholder="Enter your wallet address..."
                        required
                    >
                </div>
                <button type="submit" class="search-btn" id="searchBtn">
                    Check Fees
                </button>
            </form>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Fetching fee data, please wait...</p>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="summary-card">
                <h2>Total Claimed Fees</h2>
                <div class="total-amount" id="totalAmount">$0.00</div>
            </div>

            <div class="details-section">
                <div class="section-title">📊 Details</div>
                <div class="pagination-info" id="paginationInfo"></div>
                <div class="items-grid" id="itemsGrid"></div>
                <div class="pagination-controls" id="paginationControls"></div>
            </div>
        </div>

        <div class="info-message">
            <strong>Disclaimer:</strong>
            This tool only tracks fees after July 1, 2025. Fees from blacklisted LbPairs have not been removed.
            For official Season 2 fees verification, please use Meteora's official tools.
        </div>

        <div class="contact-section">
            <div class="contact-title">Contact</div>
            <div class="contact-links">
                <a href="https://x.com/rochestor_mu" target="_blank" class="contact-link">
                    <span class="contact-icon">𝕏</span> Twitter
                </a>
                <a href="https://github.com/gududefengzhong" target="_blank" class="contact-link">
                    <span class="contact-icon">📁</span> GitHub
                </a>
            </div>
        </div>

        <div class="donation-section">
            <div class="donation-title">☕ Buy me a coffee</div>
            <p class="donation-text">If this tool helped you, consider buying me a coffee!</p>
            <div class="wallet-address">
                <span class="wallet-label">Solana Address:</span>
                <div class="wallet-value" id="walletAddress">CpyFhZTuk8thm7UNKWWokDzMGzjt7ZHrrTMxjMi1fm6N</div>
                <button class="copy-btn" onclick="copyWalletAddress()">📋 Copy</button>
            </div>
        </div>

        <div class="footer">
            <p>Meteora Fees Checker | Last Updated: <span id="lastUpdated">-</span></p>
        </div>
    </div>

    <script>
        class MeteoraFeesChecker {
            constructor() {
                this.routerIndex = null;
                this.shardCache = new Map();
                this.currentPage = 1;
                this.pageSize = 6;
                this.allPairs = [];
                this.init();
            }

            async init() {
                try {
                    await this.loadRouterIndex();
                    this.bindEvents();
                } catch (error) {
                    this.showError('Initialization failed: ' + error.message);
                }
            }

            async loadRouterIndex() {
                try {
                    const response = await fetch('./wallet_index.json');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: Unable to load router index file, please check if file exists`);
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error(`File type error: Expected JSON file, got ${contentType}`);
                    }

                    this.routerIndex = await response.json();

                    if (this.routerIndex.lastUpdated) {
                        document.getElementById('lastUpdated').textContent =
                            new Date(this.routerIndex.lastUpdated).toLocaleString('en-US');
                    }

                } catch (error) {
                    console.error('Detailed error info:', error);
                    throw new Error(`Failed to load router index: ${error.message}`);
                }
            }

            getShardKey(walletAddress) {
                const prefixLength = this.routerIndex.shardPrefixLength || 2;
                return walletAddress.substring(0, prefixLength).toUpperCase().padEnd(prefixLength, '0');
            }

            async loadShardData(shardKey) {
                try {
                    if (this.shardCache.has(shardKey)) {
                        return this.shardCache.get(shardKey);
                    }

                    const shardInfo = this.routerIndex.shards[shardKey];
                    if (!shardInfo) {
                        return null;
                    }

                    const response = await fetch(`./shards/${shardInfo.filename}`);
                    if (!response.ok) throw new Error('Unable to load shard data');

                    const shardData = await response.json();
                    const wallets = shardData.wallets || {};

                    this.shardCache.set(shardKey, wallets);
                    return wallets;

                } catch (error) {
                    console.error('Failed to load shard data:', error);
                    return null;
                }
            }

            bindEvents() {
                document.getElementById('searchForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.searchWallet();
                });
            }

            async searchWallet() {
                const walletAddress = document.getElementById('walletAddress').value.trim();

                if (!walletAddress) {
                    this.showError('Please enter wallet address');
                    return;
                }

                this.showLoading(true);
                this.hideError();
                this.hideResults();

                try {
                    const walletInfo = await this.getWalletInfo(walletAddress);

                    if (!walletInfo || (!walletInfo.newLbPairs.length && !walletInfo.newPositions.length)) {
                        this.showError('No Season 2 data found for this wallet');
                        return;
                    }

                    await this.fetchAndDisplayFees(walletAddress, walletInfo);

                } catch (error) {
                    this.showError('Error during query: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async getWalletInfo(walletAddress) {
                const shardKey = this.getShardKey(walletAddress);
                const shardWallets = await this.loadShardData(shardKey);
                return shardWallets ? shardWallets[walletAddress] : null;
            }

            async fetchAndDisplayFees(walletAddress, walletInfo) {
                const results = {
                    pairs: [],
                    totalFees: 0
                };

                // 合并处理 lbPairs 和 positions，统一获取交易对信息
                const allItems = [];

                // 处理 lbPairs
                for (const lbPairInfo of walletInfo.newLbPairs) {
                    allItems.push({
                        type: 'lbPair',
                        address: lbPairInfo.lbPair,
                        wallet: walletAddress
                    });
                }

                // 处理 positions
                for (const positionInfo of walletInfo.newPositions) {
                    allItems.push({
                        type: 'position',
                        address: positionInfo.position,
                        wallet: walletAddress
                    });
                }

                // 统一获取每个项目的费用和交易对信息
                for (const item of allItems) {
                    try {
                        let fees, pairAddress, pairInfo;

                        if (item.type === 'lbPair') {
                            fees = await this.fetchLbPairFees(item.wallet, item.address);
                            pairAddress = item.address;
                        } else {
                            const positionData = await this.fetchPositionFees(item.address);
                            fees = positionData;
                            pairAddress = positionData?.pairAddress;
                        }

                        if (fees && pairAddress) {
                            pairInfo = await this.fetchPairInfo(pairAddress);

                            results.pairs.push({
                                pairAddress: pairAddress,
                                pairInfo: pairInfo,
                                fees: fees,
                                type: item.type,
                                originalAddress: item.address
                            });

                            results.totalFees += fees.totalFeesUsd || 0;
                        }
                    } catch (error) {
                        console.error(`Failed to fetch ${item.type} ${item.address} fees:`, error);
                    }
                }

                this.displayResults(results);
            }

            async fetchLbPairFees(wallet, lbPair) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/wallet/${wallet}/${lbPair}/earning`
                    );

                    if (!response.ok) return null;

                    const data = await response.json();
                    const totalFeesUsd = parseFloat(data.total_fee_usd_claimed) || 0;

                    return {
                        ...data,
                        totalFeesUsd: totalFeesUsd
                    };

                } catch (error) {
                    console.error('Failed to fetch lbPair fees:', error);
                    return null;
                }
            }

            async fetchPositionFees(position) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/position/${position}/claim_fees`
                    );

                    if (!response.ok) return null;

                    const claimRecords = await response.json();

                    let totalFeesUsd = 0;
                    let pairAddress = null;

                    if (Array.isArray(claimRecords) && claimRecords.length > 0) {
                        pairAddress = claimRecords[0].pair_address;

                        for (const record of claimRecords) {
                            const tokenXUsd = parseFloat(record.token_x_usd_amount) || 0;
                            const tokenYUsd = parseFloat(record.token_y_usd_amount) || 0;
                            totalFeesUsd += tokenXUsd + tokenYUsd;
                        }
                    }

                    return {
                        claimRecords: claimRecords,
                        totalFeesUsd: totalFeesUsd,
                        pairAddress: pairAddress
                    };

                } catch (error) {
                    console.error('Failed to fetch position fees:', error);
                    return null;
                }
            }

            async fetchPairInfo(pairAddress) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/pair/${pairAddress}`
                    );

                    if (!response.ok) return null;

                    const data = await response.json();

                    return {
                        name: data.name,
                        bin_step: data.bin_step,
                        base_fee_percentage: data.base_fee_percentage,
                        address: pairAddress
                    };

                } catch (error) {
                    console.error('Failed to fetch pair info:', error);
                    return null;
                }
            }

            displayResults(results) {
                document.getElementById('totalAmount').textContent =
                    '$' + results.totalFees.toFixed(2);

                // 按费用倒序排序并存储所有数据
                this.allPairs = results.pairs.sort((a, b) => (b.fees?.totalFeesUsd || 0) - (a.fees?.totalFeesUsd || 0));

                // 重置到第一页
                this.currentPage = 1;

                // 显示分页结果
                this.displayPaginatedResults();
                this.showResults();
            }

            displayPaginatedResults() {
                const itemsGrid = document.getElementById('itemsGrid');
                itemsGrid.innerHTML = '';

                const totalItems = this.allPairs.length;
                const totalPages = Math.ceil(totalItems / this.pageSize);
                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = Math.min(startIndex + this.pageSize, totalItems);

                // 显示当前页的项目
                const currentPageItems = this.allPairs.slice(startIndex, endIndex);

                currentPageItems.forEach((item, index) => {
                    const card = this.createPairCard(item);
                    card.style.setProperty('--index', index);
                    itemsGrid.appendChild(card);
                });

                // 更新分页信息和控件
                this.updatePaginationInfo(startIndex + 1, endIndex, totalItems);
                this.updatePaginationControls(totalPages);
            }

            createPairCard(item) {
                const card = document.createElement('div');
                card.className = 'item-card';

                const pairInfo = item.pairInfo;
                const pairName = pairInfo?.name || 'Unknown Pair';
                const pairAddress = item.pairAddress;
                const fees = item.fees;

                // 构造 Meteora 链接
                const meteoraUrl = `https://app.meteora.ag/dlmm/${pairAddress}`;

                // 创建可点击的交易对标题
                const pairTitleHtml = pairAddress ?
                    `<a href="${meteoraUrl}" target="_blank" class="pair-title-link">${pairName}</a>` :
                    `<span class="pair-title">${pairName}</span>`;

                const detailsHtml = `
                    <div class="detail-item">
                        <span class="detail-label">Bin Step:</span>
                        <span class="detail-value">${pairInfo?.bin_step || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Base Fee:</span>
                        <span class="detail-value">${pairInfo?.base_fee_percentage ? (pairInfo.base_fee_percentage + '%') : 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Claimed Fees:</span>
                        <span class="detail-value">$${(fees?.totalFeesUsd || 0).toFixed(2)}</span>
                    </div>
                `;

                card.innerHTML = `
                    <div class="item-header">
                        <span class="item-type pair">${pairTitleHtml}</span>
                        <span class="item-amount">$${(fees?.totalFeesUsd || 0).toFixed(2)}</span>
                    </div>
                    <div class="item-details">
                        ${detailsHtml}
                    </div>
                `;

                return card;
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                const searchBtn = document.getElementById('searchBtn');

                if (show) {
                    loading.style.display = 'block';
                    loading.classList.add('show');
                    searchBtn.disabled = true;
                    searchBtn.textContent = 'Checking...';
                    searchBtn.style.background = 'linear-gradient(45deg, #9ca3af, #6b7280)';
                } else {
                    loading.classList.remove('show');
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 300);
                    searchBtn.disabled = false;
                    searchBtn.textContent = 'Check Fees';
                    searchBtn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                }
            }

            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.classList.add('show');
                errorDiv.style.display = 'block';
            }

            hideError() {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.classList.remove('show');
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 300);
            }

            showResults() {
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.style.display = 'block';
                resultsSection.classList.add('show');
            }

            hideResults() {
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.classList.remove('show');
                setTimeout(() => {
                    resultsSection.style.display = 'none';
                }, 300);
            }

            updatePaginationInfo(start, end, total) {
                const paginationInfo = document.getElementById('paginationInfo');
                if (total === 0) {
                    paginationInfo.textContent = 'No items to display';
                } else {
                    paginationInfo.textContent = `Showing ${start}-${end} of ${total} trading pairs`;
                }
            }

            updatePaginationControls(totalPages) {
                const paginationControls = document.getElementById('paginationControls');
                paginationControls.innerHTML = '';

                if (totalPages <= 1) return;

                // Previous button
                const prevBtn = document.createElement('button');
                prevBtn.className = 'pagination-btn';
                prevBtn.textContent = '‹ Prev';
                prevBtn.disabled = this.currentPage === 1;
                prevBtn.onclick = () => this.goToPage(this.currentPage - 1);
                paginationControls.appendChild(prevBtn);

                // Page numbers
                const maxVisiblePages = 5;
                let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                if (startPage > 1) {
                    const firstBtn = document.createElement('button');
                    firstBtn.className = 'pagination-btn';
                    firstBtn.textContent = '1';
                    firstBtn.onclick = () => this.goToPage(1);
                    paginationControls.appendChild(firstBtn);

                    if (startPage > 2) {
                        const ellipsis = document.createElement('span');
                        ellipsis.textContent = '...';
                        ellipsis.style.padding = '8px';
                        ellipsis.style.color = '#718096';
                        paginationControls.appendChild(ellipsis);
                    }
                }

                for (let i = startPage; i <= endPage; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = `pagination-btn ${i === this.currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    paginationControls.appendChild(pageBtn);
                }

                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        const ellipsis = document.createElement('span');
                        ellipsis.textContent = '...';
                        ellipsis.style.padding = '8px';
                        ellipsis.style.color = '#718096';
                        paginationControls.appendChild(ellipsis);
                    }

                    const lastBtn = document.createElement('button');
                    lastBtn.className = 'pagination-btn';
                    lastBtn.textContent = totalPages;
                    lastBtn.onclick = () => this.goToPage(totalPages);
                    paginationControls.appendChild(lastBtn);
                }

                // Next button
                const nextBtn = document.createElement('button');
                nextBtn.className = 'pagination-btn';
                nextBtn.textContent = 'Next ›';
                nextBtn.disabled = this.currentPage === totalPages;
                nextBtn.onclick = () => this.goToPage(this.currentPage + 1);
                paginationControls.appendChild(nextBtn);

                // Page size selector
                const pageSizeSelector = document.createElement('div');
                pageSizeSelector.className = 'page-size-selector';
                pageSizeSelector.innerHTML = `
                    <span>Show:</span>
                    <select onchange="window.feesChecker.changePageSize(this.value)">
                        <option value="6" ${this.pageSize === 6 ? 'selected' : ''}>6</option>
                        <option value="12" ${this.pageSize === 12 ? 'selected' : ''}>12</option>
                        <option value="24" ${this.pageSize === 24 ? 'selected' : ''}>24</option>
                        <option value="50" ${this.pageSize === 50 ? 'selected' : ''}>50</option>
                    </select>
                `;
                paginationControls.appendChild(pageSizeSelector);
            }

            goToPage(page) {
                const totalPages = Math.ceil(this.allPairs.length / this.pageSize);
                if (page >= 1 && page <= totalPages) {
                    this.currentPage = page;
                    this.displayPaginatedResults();
                }
            }

            changePageSize(newSize) {
                this.pageSize = parseInt(newSize);
                this.currentPage = 1;
                this.displayPaginatedResults();
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.feesChecker = new MeteoraFeesChecker();
        });

        function copyWalletAddress() {
            const walletAddress = 'CpyFhZTuk8thm7UNKWWokDzMGzjt7ZHrrTMxjMi1fm6N';
            const copyBtn = document.querySelector('.copy-btn');

            navigator.clipboard.writeText(walletAddress).then(() => {
                // 成功复制的反馈
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ Copied!';
                copyBtn.classList.add('copy-success');

                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.classList.remove('copy-success');
                }, 2000);
            }).catch(() => {
                // 降级方案：选中文本
                const walletElement = document.getElementById('walletAddress');
                const range = document.createRange();
                range.selectNode(walletElement);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);

                copyBtn.textContent = '📋 Selected';
                setTimeout(() => {
                    copyBtn.textContent = '📋 Copy';
                }, 2000);
            });
        }
    </script>
</body>
</html>