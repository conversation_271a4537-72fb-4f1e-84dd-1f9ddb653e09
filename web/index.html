<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><PERSON><PERSON><PERSON> Checker - Season 2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: containerFadeIn 0.8s ease-out;
        }

        @keyframes containerFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: headerSlideIn 1s ease-out 0.2s both;
        }

        @keyframes headerSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            color: #718096;
            font-size: 1.1rem;
            opacity: 0.8;
            animation: subtitleFadeIn 1s ease-out 0.4s both;
        }

        @keyframes subtitleFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 0.8;
                transform: translateY(0);
            }
        }

        .header .season-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 0;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .search-section {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            animation: searchSlideUp 0.8s ease-out 0.4s both;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        @keyframes searchSlideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .search-form {
            display: flex;
            gap: 20px;
            align-items: flex-end;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2d3748;
            font-size: 1rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .search-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            position: relative;
            overflow: hidden;
        }

        .search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .search-btn:hover::before {
            left: 100%;
        }

        .search-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 15px 25px rgba(102, 126, 234, 0.4);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            animation: loadingFadeIn 0.5s ease-out;
        }

        .loading.show {
            display: block;
        }

        @keyframes loadingFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
            animation: resultsSlideIn 0.8s ease-out;
        }

        .results-section.show {
            display: block;
        }

        @keyframes resultsSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .summary-card {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(76, 175, 80, 0.3);
            animation: summaryPop 0.6s ease-out 0.2s both;
        }

        @keyframes summaryPop {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .summary-card h2 {
            font-size: 2.2rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .summary-card .total-amount {
            font-size: 3.5rem;
            font-weight: 700;
            margin: 20px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            animation: amountCount 1s ease-out 0.8s both;
        }

        @keyframes amountCount {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .details-section {
            margin-top: 40px;
            animation: detailsFadeIn 0.8s ease-out 0.6s both;
        }

        @keyframes detailsFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin: 40px 0 25px 0;
            padding-bottom: 15px;
            border-bottom: 3px solid transparent;
            background: linear-gradient(90deg, #667eea, #764ba2) 0 100% / 100% 3px no-repeat;
            animation: titleSlideIn 0.8s ease-out;
        }

        @keyframes titleSlideIn {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .items-grid {
            display: grid;
            gap: 20px;
        }

        .item-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: none;
            border-radius: 16px;
            padding: 25px;
            transition: all 0.3s ease;
            animation: cardSlideIn 0.5s ease-out calc(var(--index) * 0.1s) both;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        @keyframes cardSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .item-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .item-type {
            background: linear-gradient(45deg, #e2e8f0, #cbd5e0);
            color: #2d3748;
            padding: 6px 15px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .item-type.lb-pair {
            background: linear-gradient(45deg, #bee3f8, #90cdf4);
            color: #2b6cb0;
        }

        .item-type.position {
            background: linear-gradient(45deg, #c6f6d5, #9ae6b4);
            color: #276749;
        }

        .item-amount {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            padding: 12px 10px;
            margin: 0 -10px;
        }

        .detail-label {
            color: #718096;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .detail-value {
            color: #2d3748;
            font-weight: 600;
            font-size: 0.9rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .error-message, .info-message {
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            animation: messageSlideIn 0.5s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .error-message {
            background: linear-gradient(145deg, #fed7d7, #feb2b2);
            color: #c53030;
            border-left: 4px solid #e53e3e;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .info-message {
            background: linear-gradient(145deg, #bee3f8, #90cdf4);
            color: #2b6cb0;
            border-left: 4px solid #3182ce;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9rem;
            animation: footerFadeIn 1s ease-out 1s both;
        }

        @keyframes footerFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 5px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .header .subtitle {
                font-size: 1rem;
            }

            .search-form {
                flex-direction: column;
                gap: 10px;
            }

            .search-btn {
                width: 100%;
                padding: 15px;
            }

            .summary-card .total-amount {
                font-size: 2rem;
            }

            .items-grid {
                gap: 10px;
            }

            .item-card {
                padding: 15px;
            }

            .item-details {
                grid-template-columns: 1fr;
                gap: 5px;
            }

            .detail-value {
                font-size: 0.8rem;
                word-break: break-all;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                margin: 2px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header .season-badge {
                font-size: 0.8rem;
                padding: 3px 10px;
            }

            .summary-card {
                padding: 20px;
            }

            .summary-card .total-amount {
                font-size: 1.8rem;
            }

            .form-group input {
                font-size: 16px;
                padding: 15px;
            }

            .search-btn {
                padding: 15px;
                font-size: 16px;
            }

            .item-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .item-amount {
                font-size: 1.2rem;
            }

            .section-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💫 Meteora Fees Checker</h1>
            <div class="season-badge">Season 2 - 2025.07.01+</div>
            <p class="subtitle">查询您在Meteora第二季的累计已领取费用</p>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="walletAddress">钱包地址</label>
                    <input
                        type="text"
                        id="walletAddress"
                        placeholder="输入您的钱包地址..."
                        required
                    >
                </div>
                <button type="submit" class="search-btn" id="searchBtn">
                    查询费用
                </button>
            </form>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在查询费用数据，请稍候...</p>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="summary-card">
                <h2>总计已领取费用</h2>
                <div class="total-amount" id="totalAmount">$0.00</div>
            </div>

            <div class="details-section">
                <div class="section-title">📊 详细信息</div>
                <div class="items-grid" id="itemsGrid"></div>
            </div>
        </div>

        <div class="info-message">
            <strong>使用说明：</strong>
            此工具统计2025年7月1日以后创建的流动性池和头寸的已领取费用。
            数据通过Meteora官方API实时获取，确保准确性。
            <br><br>
            <strong>常见问题：</strong>
            <br>• 如果提示"加载路由索引失败"，请确保正确部署了所有文件
            <br>• 请输入完整的Solana钱包地址进行查询
        </div>

        <div class="footer">
            <p>Meteora Fees Checker | 数据更新时间: <span id="lastUpdated">-</span></p>
        </div>
    </div>

    <script>
        class MeteoraFeesChecker {
            constructor() {
                this.routerIndex = null;
                this.shardCache = new Map();
                this.init();
            }

            async init() {
                try {
                    await this.loadRouterIndex();
                    this.bindEvents();
                } catch (error) {
                    this.showError('初始化失败: ' + error.message);
                }
            }

            async loadRouterIndex() {
                try {
                    const response = await fetch('./wallet_index.json');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: 无法加载路由索引文件，请检查文件是否存在`);
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error(`文件类型错误: 期望JSON文件，实际返回 ${contentType}`);
                    }

                    this.routerIndex = await response.json();

                    if (this.routerIndex.lastUpdated) {
                        document.getElementById('lastUpdated').textContent =
                            new Date(this.routerIndex.lastUpdated).toLocaleString('zh-CN');
                    }

                } catch (error) {
                    console.error('详细错误信息:', error);
                    throw new Error(`加载路由索引失败: ${error.message}`);
                }
            }

            getShardKey(walletAddress) {
                const prefixLength = this.routerIndex.shardPrefixLength || 2;
                return walletAddress.substring(0, prefixLength).toUpperCase().padEnd(prefixLength, '0');
            }

            async loadShardData(shardKey) {
                try {
                    if (this.shardCache.has(shardKey)) {
                        return this.shardCache.get(shardKey);
                    }

                    const shardInfo = this.routerIndex.shards[shardKey];
                    if (!shardInfo) {
                        return null;
                    }

                    const response = await fetch(`./shards/${shardInfo.filename}`);
                    if (!response.ok) throw new Error('无法加载分片数据');

                    const shardData = await response.json();
                    const wallets = shardData.wallets || {};

                    this.shardCache.set(shardKey, wallets);
                    return wallets;

                } catch (error) {
                    console.error('加载分片数据失败:', error);
                    return null;
                }
            }

            bindEvents() {
                document.getElementById('searchForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.searchWallet();
                });
            }

            async searchWallet() {
                const walletAddress = document.getElementById('walletAddress').value.trim();

                if (!walletAddress) {
                    this.showError('请输入钱包地址');
                    return;
                }

                this.showLoading(true);
                this.hideError();
                this.hideResults();

                try {
                    const walletInfo = await this.getWalletInfo(walletAddress);

                    if (!walletInfo || (!walletInfo.newLbPairs.length && !walletInfo.newPositions.length)) {
                        this.showError('未找到该钱包在第二季的相关数据');
                        return;
                    }

                    await this.fetchAndDisplayFees(walletAddress, walletInfo);

                } catch (error) {
                    this.showError('查询过程中出错: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async getWalletInfo(walletAddress) {
                const shardKey = this.getShardKey(walletAddress);
                const shardWallets = await this.loadShardData(shardKey);
                return shardWallets ? shardWallets[walletAddress] : null;
            }

            async fetchAndDisplayFees(walletAddress, walletInfo) {
                const results = {
                    lbPairs: [],
                    positions: [],
                    totalFees: 0
                };

                for (const lbPairInfo of walletInfo.newLbPairs) {
                    try {
                        const fees = await this.fetchLbPairFees(walletAddress, lbPairInfo.lbPair);
                        if (fees) {
                            results.lbPairs.push({
                                ...lbPairInfo,
                                fees: fees
                            });
                            results.totalFees += fees.totalFeesUsd || 0;
                        }
                    } catch (error) {
                        console.error(`获取lbPair ${lbPairInfo.lbPair} 费用失败:`, error);
                    }
                }

                for (const positionInfo of walletInfo.newPositions) {
                    try {
                        const fees = await this.fetchPositionFees(positionInfo.position);
                        if (fees) {
                            results.positions.push({
                                ...positionInfo,
                                fees: fees
                            });
                            results.totalFees += fees.totalFeesUsd || 0;
                        }
                    } catch (error) {
                        console.error(`获取position ${positionInfo.position} 费用失败:`, error);
                    }
                }

                this.displayResults(results);
            }

            async fetchLbPairFees(wallet, lbPair) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/wallet/${wallet}/${lbPair}/earning`
                    );

                    if (!response.ok) return null;

                    const data = await response.json();
                    const totalFeesUsd = parseFloat(data.total_fee_usd_claimed) || 0;

                    return {
                        ...data,
                        totalFeesUsd: totalFeesUsd
                    };

                } catch (error) {
                    console.error('获取lbPair费用失败:', error);
                    return null;
                }
            }

            async fetchPositionFees(position) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/position/${position}/claim_fees`
                    );

                    if (!response.ok) return null;

                    const claimRecords = await response.json();

                    let totalFeesUsd = 0;
                    let pairAddress = null;

                    if (Array.isArray(claimRecords) && claimRecords.length > 0) {
                        pairAddress = claimRecords[0].pair_address;

                        for (const record of claimRecords) {
                            const tokenXUsd = parseFloat(record.token_x_usd_amount) || 0;
                            const tokenYUsd = parseFloat(record.token_y_usd_amount) || 0;
                            totalFeesUsd += tokenXUsd + tokenYUsd;
                        }
                    }

                    let pairInfo = null;
                    if (pairAddress) {
                        pairInfo = await this.fetchPairInfo(pairAddress);
                    }

                    return {
                        claimRecords: claimRecords,
                        totalFeesUsd: totalFeesUsd,
                        pairAddress: pairAddress,
                        pairInfo: pairInfo
                    };

                } catch (error) {
                    console.error('获取position费用失败:', error);
                    return null;
                }
            }

            async fetchPairInfo(pairAddress) {
                try {
                    const response = await fetch(
                        `https://dlmm-api.meteora.ag/pair/${pairAddress}`
                    );

                    if (!response.ok) return null;

                    const data = await response.json();

                    return {
                        name: data.name,
                        bin_step: data.bin_step,
                        base_fee_percentage: data.base_fee_percentage
                    };

                } catch (error) {
                    console.error('获取pair信息失败:', error);
                    return null;
                }
            }

            displayResults(results) {
                document.getElementById('totalAmount').textContent =
                    '$' + results.totalFees.toFixed(2);

                const itemsGrid = document.getElementById('itemsGrid');
                itemsGrid.innerHTML = '';

                let index = 0;

                results.lbPairs.forEach(item => {
                    const card = this.createItemCard('lb-pair', item);
                    card.style.setProperty('--index', index++);
                    itemsGrid.appendChild(card);
                });

                results.positions.forEach(item => {
                    const card = this.createItemCard('position', item);
                    card.style.setProperty('--index', index++);
                    itemsGrid.appendChild(card);
                });

                this.showResults();
            }

            createItemCard(type, item) {
                const card = document.createElement('div');
                card.className = 'item-card';

                const typeLabel = type === 'lb-pair' ? '流动性池' : '头寸';

                let detailsHtml = '';

                if (type === 'lb-pair') {
                    const identifier = item.lbPair;
                    detailsHtml = `
                        <div class="detail-item">
                            <span class="detail-label">LB Pair:</span>
                            <span class="detail-value">${identifier.substring(0, 8)}...${identifier.substring(-8)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">费用类型:</span>
                            <span class="detail-value">流动性池费用</span>
                        </div>
                    `;
                } else {
                    const pairInfo = item.fees?.pairInfo;
                    console.log('Position pairInfo:', pairInfo);

                    if (pairInfo && pairInfo.name) {
                        detailsHtml = `
                            <div class="detail-item">
                                <span class="detail-label">交易对:</span>
                                <span class="detail-value">${pairInfo.name}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Bin Step:</span>
                                <span class="detail-value">${pairInfo.bin_step || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">基础费率:</span>
                                <span class="detail-value">${pairInfo.base_fee_percentage ? (pairInfo.base_fee_percentage + '%') : 'N/A'}</span>
                            </div>
                        `;
                    } else {
                        const pairAddress = item.fees?.pairAddress;
                        detailsHtml = `
                            <div class="detail-item">
                                <span class="detail-label">Pair地址:</span>
                                <span class="detail-value">${pairAddress ? (pairAddress.substring(0, 8) + '...' + pairAddress.substring(-8)) : 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">费用类型:</span>
                                <span class="detail-value">头寸费用</span>
                            </div>
                        `;
                    }
                }

                card.innerHTML = `
                    <div class="item-header">
                        <span class="item-type ${type}">${typeLabel}</span>
                        <span class="item-amount">$${(item.fees?.totalFeesUsd || 0).toFixed(2)}</span>
                    </div>
                    <div class="item-details">
                        ${detailsHtml}
                    </div>
                `;

                return card;
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                const searchBtn = document.getElementById('searchBtn');

                if (show) {
                    loading.style.display = 'block';
                    loading.classList.add('show');
                    searchBtn.disabled = true;
                    searchBtn.textContent = '查询中...';
                    searchBtn.style.background = 'linear-gradient(45deg, #9ca3af, #6b7280)';
                } else {
                    loading.classList.remove('show');
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 300);
                    searchBtn.disabled = false;
                    searchBtn.textContent = '查询费用';
                    searchBtn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                }
            }

            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.classList.add('show');
                errorDiv.style.display = 'block';
            }

            hideError() {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.classList.remove('show');
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 300);
            }

            showResults() {
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.style.display = 'block';
                resultsSection.classList.add('show');
            }

            hideResults() {
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.classList.remove('show');
                setTimeout(() => {
                    resultsSection.style.display = 'none';
                }, 300);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new MeteoraFeesChecker();
        });
    </script>
</body>
</html>