<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>er - S2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .season-info {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .data-status {
            background: rgba(255,255,255,0.1);
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.8rem;
            text-align: left;
        }

        .main-content {
            padding: 40px 30px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .input-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(33, 150, 243, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .query-mode {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.9rem;
        }

        .mode-btn.active {
            border-color: #2196F3;
            background: #f3f9ff;
            color: #2196F3;
            font-weight: 600;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            display: none;
            margin-top: 30px;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .result-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .fee-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
            color: #2196F3;
        }

        .fee-label {
            color: #666;
        }

        .fee-value {
            font-weight: 600;
            color: #333;
        }

        .total-fee {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
        }

        .total-fee h2 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #c62828;
        }

        .warning {
            background: #fff3e0;
            color: #f57c00;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #f57c00;
        }

        .details {
            margin-top: 20px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .details h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .details ul {
            list-style: none;
            padding: 0;
        }

        .details li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
        }

        .pair-address {
            font-family: monospace;
            font-size: 0.9rem;
            color: #666;
            word-break: break-all;
            max-width: 60%;
        }

        .cached-badge {
            background: #4CAF50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            margin-left: 10px;
        }

        .live-badge {
            background: #ff9800;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 30px 20px;
            }

            .fee-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .query-mode {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Meteora Fees Checker</h1>
            <p>Season 2 费用查询工具 - GitHub静态版</p>
            <div class="season-info">
                <strong>统计范围:</strong> 2025-07-01 00:00:00 UTC 至今<br>
                <strong>统计口径:</strong> 已领取的 fees（不包含 rewards）
            </div>
            <div class="data-status" id="dataStatus">
                <div>🔄 正在加载数据状态...</div>
            </div>
        </div>

        <div class="main-content">
            <div class="input-section">
                <div class="query-mode">
                    <div class="mode-btn active" onclick="setQueryMode('cached')" id="cachedMode">
                        📊 缓存查询 (快速)
                    </div>
                    <div class="mode-btn" onclick="setQueryMode('live')" id="liveMode">
                        🔴 实时查询 (最新)
                    </div>
                </div>

                <div class="input-group">
                    <label for="walletAddress">钱包地址 (Wallet Address)</label>
                    <input
                        type="text"
                        id="walletAddress"
                        placeholder="请输入您的 Solana 钱包地址..."
                        autocomplete="off"
                    >
                </div>
                <button class="btn" onclick="calculateFees()">
                    🔍 查询费用
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p id="loadingText">正在查询费用，请稍候...</p>
            </div>

            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <h3>💰 费用明细 <span id="queryBadge"></span></h3>
                    <div class="fee-item">
                        <span class="fee-label">LbPair 费用 (7.1后创建的流动池)</span>
                        <span class="fee-value" id="lbpairFees">$0.00</span>
                    </div>
                    <div class="fee-item">
                        <span class="fee-label">Position 费用 (7.1前创建的流动池)</span>
                        <span class="fee-value" id="positionFees">$0.00</span>
                    </div>
                    <div class="fee-item">
                        <span class="fee-label">总费用</span>
                        <span class="fee-value" id="totalFees">$0.00</span>
                    </div>
                </div>

                <div class="total-fee">
                    <h2 id="totalAmount">$0.00</h2>
                    <p>累计已领取费用 (USD)</p>
                </div>

                <div class="details" id="details" style="display: none;">
                    <h4>📊 详细信息</h4>
                    <div id="detailsContent"></div>
                </div>
            </div>

            <div class="warning" id="warningSection" style="display: none;">
                <strong>⚠️ 注意</strong>
                <p id="warningMessage"></p>
            </div>

            <div class="error" id="errorSection" style="display: none;">
                <strong>❌ 查询失败</strong>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let currentQueryMode = 'cached';  // 'cached' 或 'live'
        let walletIndex = {};
        let dataMetadata = null;

        // CORS代理配置
        const CORS_PROXY = 'https://api.allorigins.win/get?url=';
        const BACKUP_PROXY = 'https://corsproxy.io/?';

        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            await loadDataStatus();

            // 添加回车键支持
            document.getElementById('walletAddress').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    calculateFees();
                }
            });
        });

        // 加载数据状态
        async function loadDataStatus() {
            try {
                const metadataResponse = await fetch('./meteora_data/metadata.json');
                if (metadataResponse.ok) {
                    dataMetadata = await metadataResponse.json();

                    const lastUpdate = new Date(dataMetadata.last_updated);
                    const now = new Date();
                    const hoursSinceUpdate = (now - lastUpdate) / (1000 * 60 * 60);

                    let statusHTML = `
                        <div>📅 数据更新时间: ${lastUpdate.toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}</div>
                        <div>📊 分片数量: ${dataMetadata.total_shards || 'N/A'}</div>
                    `;

                    if (hoursSinceUpdate > 25) {
                        statusHTML += `<div style="color: #ff9800;">⚠️ 数据可能不是最新的 (${hoursSinceUpdate.toFixed(1)}小时前)</div>`;
                    } else {
                        statusHTML += `<div style="color: #4CAF50;">✅ 数据较新 (${hoursSinceUpdate.toFixed(1)}小时前)</div>`;
                    }

                    document.getElementById('dataStatus').innerHTML = statusHTML;
                } else {
                    throw new Error('无法加载元数据');
                }

                // 加载钱包索引
                const indexResponse = await fetch('./api/wallet_index.json');
                if (indexResponse.ok) {
                    walletIndex = await indexResponse.json();
                    console.log(`已加载 ${Object.keys(walletIndex).length} 个钱包的索引`);
                } else {
                    console.warn('无法加载钱包索引，将使用实时查询');
                }

            } catch (error) {
                console.error('加载数据状态失败:', error);
                document.getElementById('dataStatus').innerHTML =
                    '<div style="color: #f44336;">❌ 无法加载数据状态，将使用实时查询模式</div>';
            }
        }

        // 设置查询模式
        function setQueryMode(mode) {
            currentQueryMode = mode;

            document.getElementById('cachedMode').classList.toggle('active', mode === 'cached');
            document.getElementById('liveMode').classList.toggle('active', mode === 'live');

            // 更新说明文本
            const modeText = mode === 'cached' ? '使用预计算数据，查询速度快' : '实时查询最新数据，可能较慢';

            if (mode === 'live') {
                showWarning('实时查询模式会直接访问Meteora API，查询时间较长，请耐心等待。');
            } else {
                hideWarning();
            }
        }

        // 计算费用主函数
        async function calculateFees() {
            const walletAddress = document.getElementById('walletAddress').value.trim().toLowerCase();

            if (!walletAddress) {
                showError('请输入钱包地址');
                return;
            }

            if (!isValidSolanaAddress(walletAddress)) {
                showError('请输入有效的 Solana 钱包地址');
                return;
            }

            showLoading(true);
            hideError();
            hideWarning();
            hideResult();

            try {
                let result;

                if (currentQueryMode === 'cached') {
                    result = await getCachedFees(walletAddress);
                } else {
                    result = await getLiveFees(walletAddress);
                }

                if (result) {
                    showResult(result);
                } else {
                    showError('未找到该钱包的费用数据');
                }

            } catch (error) {
                console.error('计算费用失败:', error);
                showError('计算费用时发生错误: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 获取缓存的费用数据
        async function getCachedFees(wallet) {
            const shard = walletIndex[wallet];

            if (!shard) {
                throw new Error('该钱包未在我们的数据库中找到，请尝试实时查询模式');
            }

            try {
                const response = await fetch(`./api/fees/${shard}.json`);
                if (!response.ok) {
                    throw new Error('无法加载费用数据分片');
                }

                const shardData = await response.json();
                const walletData = shardData[wallet];

                if (!walletData) {
                    throw new Error('该钱包的费用数据不存在');
                }

                if (walletData.error) {
                    throw new Error(`数据错误: ${walletData.error}`);
                }

                return {
                    wallet: wallet,
                    lbpairFees: walletData.lbpair_fees || 0,
                    positionFees: walletData.position_fees || 0,
                    totalFees: walletData.total_fees || 0,
                    lbpairDetails: walletData.lbpair_details || [],
                    positionDetails: walletData.position_details || [],
                    lastUpdated: walletData.last_updated,
                    queryMode: 'cached'
                };

            } catch (error) {
                console.error('获取缓存费用失败:', error);
                throw error;
            }
        }

        // 获取实时费用数据
        async function getLiveFees(wallet) {
            document.getElementById('loadingText').textContent = '正在获取钱包映射关系...';

            // 先尝试从索引获取映射关系
            const shard = walletIndex[wallet];
            let walletMappings = null;

            if (shard) {
                try {
                    const response = await fetch(`./meteora_data/shards/${shard}.json`);
                    if (response.ok) {
                        const shardData = await response.json();
                        walletMappings = shardData.wallets[wallet];
                    }
                } catch (error) {
                    console.warn('无法加载分片数据，将跳过实时查询');
                }
            }

            if (!walletMappings) {
                throw new Error('未找到该钱包的映射关系，无法进行实时查询');
            }

            document.getElementById('loadingText').textContent = '正在查询LbPair费用...';

            // 查询LbPair费用
            const lbpairResult = await calculateLiveLbpairFees(wallet, walletMappings.lbpairs || []);

            document.getElementById('loadingText').textContent = '正在查询Position费用...';

            // 查询Position费用
            const positionResult = await calculateLivePositionFees(walletMappings.positions || []);

            const totalFees = lbpairResult.total + positionResult.total;

            return {
                wallet: wallet,
                lbpairFees: lbpairResult.total,
                positionFees: positionResult.total,
                totalFees: totalFees,
                lbpairDetails: lbpairResult.details,
                positionDetails: positionResult.details,
                lastUpdated: new Date().toISOString(),
                queryMode: 'live'
            };
        }

        // 实时查询LbPair费用
        async function calculateLiveLbpairFees(wallet, lbpairs) {
            let total = 0;
            const details = [];

            for (let i = 0; i < lbpairs.length; i++) {
                const lbpair = lbpairs[i];
                try {
                    const fees = await fetchWithProxy(`https://dlmm-api.meteora.ag/wallet/${wallet}/${lbpair}/earning`);
                    const feeAmount = fees.total_fee_usd_claimed || 0;

                    total += feeAmount;
                    details.push({ lbpair, fees: feeAmount });

                    // 更新进度
                    document.getElementById('loadingText').textContent =
                        `正在查询LbPair费用... (${i + 1}/${lbpairs.length})`;

                    // 避免API限制
                    await sleep(200);
                } catch (error) {
                    console.error(`获取LbPair ${lbpair}费用失败:`, error);
                    details.push({ lbpair, fees: 0, error: error.message });
                }
            }

            return { total, details };
        }

        // 实时查询Position费用
        async function calculateLivePositionFees(positions) {
            let total = 0;
            const details = [];

            for (let i = 0; i < positions.length; i++) {
                const positionInfo = positions[i];
                try {
                    const fees = await fetchWithProxy(`https://dlmm-api.meteora.ag/position_v2/${positionInfo.position}`);
                    const feeAmount = fees.total_fee_usd_claimed || 0;

                    total += feeAmount;
                    details.push({
                        position: positionInfo.position,
                        lbpair: positionInfo.lbpair,
                        fees: feeAmount
                    });

                    // 更新进度
                    document.getElementById('loadingText').textContent =
                        `正在查询Position费用... (${i + 1}/${positions.length})`;

                    // 避免API限制
                    await sleep(200);
                } catch (error) {
                    console.error(`获取Position ${positionInfo.position}费用失败:`, error);
                    details.push({
                        position: positionInfo.position,
                        lbpair: positionInfo.lbpair,
                        fees: 0,
                        error: error.message
                    });
                }
            }

            return { total, details };
        }

        // 带代理的API请求
        async function fetchWithProxy(url) {
            const proxies = [CORS_PROXY, BACKUP_PROXY];

            for (const proxy of proxies) {
                try {
                    const proxyUrl = proxy + encodeURIComponent(url);
                    const response = await fetch(proxyUrl, { timeout: 15000 });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();

                    // 处理不同代理的响应格式
                    if (data.contents) {
                        return JSON.parse(data.contents);
                    } else {
                        return data;
                    }
                } catch (error) {
                    console.warn(`代理 ${proxy} 失败:`, error);
                    if (proxy === proxies[proxies.length - 1]) {
                        throw new Error('所有代理都失败了');
                    }
                }
            }
        }

        // 显示结果
        function showResult(result) {
            document.getElementById('lbpairFees').textContent = `$${result.lbpairFees.toFixed(4)}`;
            document.getElementById('positionFees').textContent = `$${result.positionFees.toFixed(4)}`;
            document.getElementById('totalFees').textContent = `$${result.totalFees.toFixed(4)}`;
            document.getElementById('totalAmount').textContent = `$${result.totalFees.toFixed(4)}`;

            // 显示查询模式标识
            const badge = result.queryMode === 'cached' ?
                '<span class="cached-badge">缓存数据</span>' :
                '<span class="live-badge">实时数据</span>';
            document.getElementById('queryBadge').innerHTML = badge;

            // 显示详细信息
            if (result.lbpairDetails.length > 0 || result.positionDetails.length > 0) {
                let detailsHTML = '';

                if (result.lbpairDetails.length > 0) {
                    detailsHTML += '<h5>LbPair 详情:</h5><ul>';
                    result.lbpairDetails.forEach(item => {
                        const errorText = item.error ? ` (错误: ${item.error})` : '';
                        detailsHTML += `<li>
                            <span class="pair-address">${item.lbpair}${errorText}</span>
                            <span>$${item.fees.toFixed(4)}</span>
                        </li>`;
                    });
                    detailsHTML += '</ul>';
                }

                if (result.positionDetails.length > 0) {
                    detailsHTML += '<h5>Position 详情:</h5><ul>';
                    result.positionDetails.forEach(item => {
                        const errorText = item.error ? ` (错误: ${item.error})` : '';
                        detailsHTML += `<li>
                            <span class="pair-address">${item.position}${errorText}</span>
                            <span>$${item.fees.toFixed(4)}</span>
                        </li>`;
                    });
                    detailsHTML += '</ul>';
                }

                if (result.lastUpdated) {
                    const updateTime = new Date(result.lastUpdated).toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'});
                    detailsHTML += `<p style="margin-top: 15px; color: #666; font-size: 0.9rem;">数据更新时间: ${updateTime}</p>`;
                }

                document.getElementById('detailsContent').innerHTML = detailsHTML;
                document.getElementById('details').style.display = 'block';
            }

            document.getElementById('resultSection').style.display = 'block';
        }

        // 其他辅助函数保持不变...
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.querySelector('.btn').disabled = show;
            if (!show) {
                document.getElementById('loadingText').textContent = '正在查询费用，请稍候...';
            }
        }

        function hideResult() {
            document.getElementById('resultSection').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorSection').style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorSection').style.display = 'none';
        }

        function showWarning(message) {
            document.getElementById('warningMessage').textContent = message;
            document.getElementById('warningSection').style.display = 'block';
        }

        function hideWarning() {
            document.getElementById('warningSection').style.display = 'none';
        }

        function isValidSolanaAddress(address) {
            return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>