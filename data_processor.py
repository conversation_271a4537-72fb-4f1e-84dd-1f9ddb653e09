#!/usr/bin/env python3
"""
Meteora Fees Checker - 数据处理脚本
将原始JSONL数据处理为优化的前端索引文件
"""

import json
import os
from collections import defaultdict
from typing import Dict, List, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataProcessor:
    def __init__(self):
        """初始化数据处理器"""
        self.wallet_index: Dict[str, Dict] = defaultdict(lambda: {
            "newLbPairs": [],
            "newPositions": []
        })

        # 确保输出目录存在
        os.makedirs("web", exist_ok=True)

    def load_lb_pairs_data(self) -> int:
        """加载lbPair数据"""
        logger.info("加载lbPair数据...")

        file_path = "data/new_lb_pairs.jsonl"
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return 0

        count = 0
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        record = json.loads(line.strip())
                        wallet = record.get("wallet", "").lower()
                        lb_pair = record.get("lb_pair", "")
                        created_at = record.get("created_at", "")

                        if wallet and lb_pair:
                            lb_pair_info = {
                                "lbPair": lb_pair,
                                "createdAt": created_at
                            }

                            # 避免重复添加
                            existing_pairs = [p["lbPair"] for p in self.wallet_index[wallet]["newLbPairs"]]
                            if lb_pair not in existing_pairs:
                                self.wallet_index[wallet]["newLbPairs"].append(lb_pair_info)
                                count += 1

                    except json.JSONDecodeError as e:
                        logger.warning(f"解析JSON行失败: {e}")
                        continue

        except Exception as e:
            logger.error(f"加载lbPair数据时出错: {e}")
            return 0

        logger.info(f"成功加载 {count} 条lbPair记录")
        return count

    def load_positions_data(self) -> int:
        """加载position数据"""
        logger.info("加载position数据...")

        file_path = "data/new_positions.jsonl"
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return 0

        count = 0
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        record = json.loads(line.strip())
                        wallet = record.get("wallet", "").lower()
                        position = record.get("position", "")
                        lb_pair = record.get("lb_pair", "")
                        created_at = record.get("created_at", "")

                        if wallet and position:
                            position_info = {
                                "position": position,
                                "lbPair": lb_pair,
                                "createdAt": created_at
                            }

                            # 避免重复添加
                            existing_positions = [p["position"] for p in self.wallet_index[wallet]["newPositions"]]
                            if position not in existing_positions:
                                self.wallet_index[wallet]["newPositions"].append(position_info)
                                count += 1

                    except json.JSONDecodeError as e:
                        logger.warning(f"解析JSON行失败: {e}")
                        continue

        except Exception as e:
            logger.error(f"加载position数据时出错: {e}")
            return 0

        logger.info(f"成功加载 {count} 条position记录")
        return count

    def generate_wallet_index(self) -> bool:
        """生成钱包索引文件"""
        logger.info("生成钱包索引文件...")

        try:
            # 清理空数据
            cleaned_index = {}
            for wallet, data in self.wallet_index.items():
                if data["newLbPairs"] or data["newPositions"]:
                    cleaned_index[wallet] = data

            # 生成主索引文件
            output_data = {
                "lastUpdated": self._get_current_timestamp(),
                "totalWallets": len(cleaned_index),
                "totalLbPairs": sum(len(data["newLbPairs"]) for data in cleaned_index.values()),
                "totalPositions": sum(len(data["newPositions"]) for data in cleaned_index.values()),
                "wallets": cleaned_index
            }

            # 检查文件大小
            estimated_size = len(json.dumps(output_data, ensure_ascii=False))
            logger.info(f"预估文件大小: {estimated_size / (1024 * 1024):.2f} MB")

            if estimated_size > 90 * 1024 * 1024:  # 90MB限制，保留10MB缓冲
                logger.warning("文件过大，将进行分片处理")
                return self._generate_sharded_index(cleaned_index)

            # 写入单个文件
            with open("web/wallet_index.json", 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, separators=(',', ':'))

            logger.info(f"钱包索引文件已生成: web/wallet_index.json")
            logger.info(f"包含 {len(cleaned_index)} 个钱包的数据")

            return True

        except Exception as e:
            logger.error(f"生成钱包索引时出错: {e}")
            return False

    def _generate_sharded_index(self, wallet_data: Dict) -> bool:
        """生成分片索引文件"""
        logger.info("生成分片索引文件...")

        try:
            # 按钱包地址的第一个字符分片
            shards = defaultdict(dict)
            for wallet, data in wallet_data.items():
                shard_key = wallet[0] if wallet else '0'
                shards[shard_key][wallet] = data

            # 生成主索引映射
            shard_info = {}
            for shard_key, shard_data in shards.items():
                shard_filename = f"wallet_shard_{shard_key}.json"
                shard_info[shard_key] = {
                    "filename": shard_filename,
                    "walletCount": len(shard_data),
                    "lbPairCount": sum(len(data["newLbPairs"]) for data in shard_data.values()),
                    "positionCount": sum(len(data["newPositions"]) for data in shard_data.values())
                }

                # 写入分片文件
                shard_output = {
                    "shard": shard_key,
                    "lastUpdated": self._get_current_timestamp(),
                    "wallets": shard_data
                }

                with open(f"web/{shard_filename}", 'w', encoding='utf-8') as f:
                    json.dump(shard_output, f, ensure_ascii=False, separators=(',', ':'))

                logger.info(f"分片 {shard_key} 已生成: {len(shard_data)} 个钱包")

            # 生成主索引
            main_index = {
                "lastUpdated": self._get_current_timestamp(),
                "isSharded": True,
                "totalWallets": len(wallet_data),
                "totalLbPairs": sum(len(data["newLbPairs"]) for data in wallet_data.values()),
                "totalPositions": sum(len(data["newPositions"]) for data in wallet_data.values()),
                "shards": shard_info
            }

            with open("web/wallet_index.json", 'w', encoding='utf-8') as f:
                json.dump(main_index, f, ensure_ascii=False, separators=(',', ':'))

            logger.info(f"主索引已生成，包含 {len(shards)} 个分片")
            return True

        except Exception as e:
            logger.error(f"生成分片索引时出错: {e}")
            return False

    def generate_stats_file(self) -> bool:
        """生成统计信息文件"""
        logger.info("生成统计信息文件...")

        try:
            # 统计各种数据
            total_wallets = len(self.wallet_index)
            total_lb_pairs = sum(len(data["newLbPairs"]) for data in self.wallet_index.values())
            total_positions = sum(len(data["newPositions"]) for data in self.wallet_index.values())

            # 钱包分布统计
            wallet_distribution = defaultdict(int)
            for wallet, data in self.wallet_index.items():
                total_items = len(data["newLbPairs"]) + len(data["newPositions"])
                if total_items == 0:
                    continue
                elif total_items <= 5:
                    wallet_distribution["1-5"] += 1
                elif total_items <= 20:
                    wallet_distribution["6-20"] += 1
                elif total_items <= 50:
                    wallet_distribution["21-50"] += 1
                else:
                    wallet_distribution["50+"] += 1

            stats = {
                "lastUpdated": self._get_current_timestamp(),
                "summary": {
                    "totalWallets": total_wallets,
                    "totalLbPairs": total_lb_pairs,
                    "totalPositions": total_positions,
                    "totalItems": total_lb_pairs + total_positions
                },
                "distribution": dict(wallet_distribution),
                "version": "1.0.0"
            }

            with open("web/stats.json", 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)

            logger.info("统计信息文件已生成: web/stats.json")
            return True

        except Exception as e:
            logger.error(f"生成统计信息时出错: {e}")
            return False

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"

    def process_all_data(self) -> Dict[str, int]:
        """处理所有数据"""
        logger.info("开始处理所有数据...")

        results = {
            "lb_pairs_loaded": 0,
            "positions_loaded": 0,
            "files_generated": 0
        }

        try:
            # 加载数据
            results["lb_pairs_loaded"] = self.load_lb_pairs_data()
            results["positions_loaded"] = self.load_positions_data()

            # 生成输出文件
            if self.generate_wallet_index():
                results["files_generated"] += 1

            if self.generate_stats_file():
                results["files_generated"] += 1

            logger.info(f"数据处理完成: {results}")

        except Exception as e:
            logger.error(f"数据处理过程中出错: {e}")

        return results


def main():
    """主函数"""
    try:
        processor = DataProcessor()
        results = processor.process_all_data()

        print("\n" + "=" * 50)
        print("Meteora Fees Checker - 数据处理完成")
        print("=" * 50)
        print(f"lbPair记录: {results['lb_pairs_loaded']}")
        print(f"position记录: {results['positions_loaded']}")
        print(f"生成文件: {results['files_generated']}")
        print("\n生成的文件:")

        # 列出生成的文件
        web_files = []
        if os.path.exists("web"):
            for file in os.listdir("web"):
                if file.endswith('.json'):
                    file_path = os.path.join("web", file)
                    file_size = os.path.getsize(file_path)
                    web_files.append(f"  - {file} ({file_size / 1024:.1f} KB)")

        if web_files:
            print("\n".join(web_files))
        else:
            print("  无文件生成")

        print("\n下一步: 部署 web/ 目录到静态托管平台")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
