#!/usr/bin/env python3
"""
Meteora Fees Checker - 数据处理脚本
将原始JSONL数据处理为按地址前缀分片的索引文件
以空间换时间，支持快速查询和长期扩展
"""

import json
import os
from collections import defaultdict
from typing import Dict, List, Set
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataProcessor:
    def __init__(self, config_path: str = "config.json"):
        """初始化数据处理器"""
        self.config = self._load_config(config_path)

        # 分片配置 - 按地址前2个字符分片 (256个分片)
        self.shard_prefix_length = self.config.get("data_settings", {}).get("shard_prefix_length", 2)
        self.max_file_size_mb = self.config.get("data_settings", {}).get("max_file_size_mb", 50)

        # 数据存储：按分片组织
        self.shard_data: Dict[str, Dict[str, Dict]] = defaultdict(lambda: defaultdict(lambda: {
            "newLbPairs": [],
            "newPositions": []
        }))

        # 确保输出目录存在
        os.makedirs("web", exist_ok=True)
        os.makedirs("web/shards", exist_ok=True)

        # 加载现有分片数据
        self._load_existing_shards()

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
            return {}

    def _get_shard_key(self, wallet_address: str) -> str:
        """根据Solana钱包地址获取分片键"""
        if not wallet_address:
            logger.warning("钱包地址为空")
            return "00"

        # Solana地址是base58编码，直接取前N个字符作为分片键
        shard_key = wallet_address[:self.shard_prefix_length].upper()

        # 确保分片键长度一致，不足时用'0'填充
        if len(shard_key) < self.shard_prefix_length:
            shard_key = shard_key.ljust(self.shard_prefix_length, '0')

        return shard_key

    def _load_existing_shards(self):
        """加载现有的分片数据"""
        shard_count = 0
        wallet_count = 0

        try:
            # 扫描分片文件
            shard_dir = "web/shards"
            if os.path.exists(shard_dir):
                for filename in os.listdir(shard_dir):
                    if filename.startswith("shard_") and filename.endswith(".json"):
                        shard_key = filename.replace("shard_", "").replace(".json", "")

                        with open(os.path.join(shard_dir, filename), 'r', encoding='utf-8') as f:
                            shard_data = json.load(f)
                            wallets = shard_data.get("wallets", {})

                            for wallet, wallet_data in wallets.items():
                                self.shard_data[shard_key][wallet] = wallet_data
                                wallet_count += 1

                            shard_count += 1

            if shard_count > 0:
                logger.info(f"已加载 {shard_count} 个分片，包含 {wallet_count} 个钱包")

        except Exception as e:
            logger.warning(f"加载现有分片失败: {e}")

    def load_lb_pairs_data(self) -> int:
        """加载lbPair数据到分片（强制处理所有数据）"""
        logger.info("加载lbPair数据到分片...")

        file_path = "data/new_lb_pairs.jsonl"
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return 0

        # 临时：清空现有数据，强制重新处理所有JSONL数据
        for shard_key in list(self.shard_data.keys()):
            for wallet in list(self.shard_data[shard_key].keys()):
                self.shard_data[shard_key][wallet]["newLbPairs"] = []

        new_count = 0
        total_count = 0
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        record = json.loads(line.strip())
                        wallet = record.get("wallet", "")
                        lb_pair = record.get("lb_pair", "")
                        fetched_at = record.get("fetched_at", "")

                        total_count += 1

                        if wallet and lb_pair:
                            # 确定分片
                            shard_key = self._get_shard_key(wallet)

                            lb_pair_info = {
                                "lbPair": lb_pair,
                                "fetchedAt": fetched_at
                            }

                            # 强制添加所有数据，不进行去重检查
                            self.shard_data[shard_key][wallet]["newLbPairs"].append(lb_pair_info)
                            new_count += 1

                    except json.JSONDecodeError as e:
                        logger.warning(f"解析JSON行失败: {e}")
                        continue

        except Exception as e:
            logger.error(f"加载lbPair数据时出错: {e}")
            return 0

        logger.info(f"lbPair数据: 总计 {total_count} 条，强制处理 {new_count} 条记录")
        return new_count

    def load_positions_data(self) -> int:
        """加载position数据到分片（强制处理所有数据）"""
        logger.info("加载position数据到分片...")

        file_path = "data/new_positions.jsonl"
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return 0

        # 临时：清空现有数据，强制重新处理所有JSONL数据
        for shard_key in list(self.shard_data.keys()):
            for wallet in list(self.shard_data[shard_key].keys()):
                self.shard_data[shard_key][wallet]["newPositions"] = []

        new_count = 0
        total_count = 0
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        record = json.loads(line.strip())
                        wallet = record.get("wallet", "")
                        position = record.get("position", "")
                        fetched_at = record.get("fetched_at", "")

                        total_count += 1

                        if wallet and position:
                            # 确定分片
                            shard_key = self._get_shard_key(wallet)

                            position_info = {
                                "position": position,
                                "fetchedAt": fetched_at
                            }

                            # 强制添加所有数据，不进行去重检查
                            self.shard_data[shard_key][wallet]["newPositions"].append(position_info)
                            new_count += 1

                    except json.JSONDecodeError as e:
                        logger.warning(f"解析JSON行失败: {e}")
                        continue

        except Exception as e:
            logger.error(f"加载position数据时出错: {e}")
            return 0

        logger.info(f"position数据: 总计 {total_count} 条，强制处理 {new_count} 条记录")
        return new_count

    def generate_shard_files(self) -> bool:
        """生成分片文件"""
        logger.info("生成分片文件...")

        try:
            # 统计信息
            total_wallets = 0
            total_lb_pairs = 0
            total_positions = 0

            test_address = "CpyFhZTuk8thm7UNKWWokDzMGzjt7ZHrrTMxjMi1fm6N"
            test_shard_key = self._get_shard_key(test_address)
            logger.info(f"调试：地址 {test_address} 的分片键为 {test_shard_key}")

            # 检查该地址是否在shard_data中
            if test_shard_key in self.shard_data and test_address in self.shard_data[test_shard_key]:
                test_wallet_data = self.shard_data[test_shard_key][test_address]
                logger.info(f"在shard_data中找到地址 {test_address}:")
                logger.info(f"  - lbPairs数量: {len(test_wallet_data['newLbPairs'])}")
                logger.info(f"  - positions数量: {len(test_wallet_data['newPositions'])}")
                logger.info(f"  - lbPairs内容: {test_wallet_data['newLbPairs']}")
                logger.info(f"  - positions内容: {test_wallet_data['newPositions']}")
            else:
                logger.warning(f"在shard_data中未找到地址 {test_address}")

            # 为每个分片生成文件
            generated_shards = 0
            for shard_key, shard_wallets in self.shard_data.items():
                if not shard_wallets:
                    continue

                # 不进行空数据过滤，强制包含所有钱包
                valid_wallets = {}
                for wallet, wallet_data in shard_wallets.items():
                    # 强制包含所有钱包，即使数据为空
                    valid_wallets[wallet] = wallet_data
                    total_wallets += 1
                    total_lb_pairs += len(wallet_data["newLbPairs"])
                    total_positions += len(wallet_data["newPositions"])

                if not valid_wallets:
                    continue

                # 生成分片文件
                shard_filename = f"shard_{shard_key}.json"

                shard_data = {
                    "shardKey": shard_key,
                    "lastUpdated": self._get_current_timestamp(),
                    "walletCount": len(valid_wallets),
                    "wallets": valid_wallets
                }

                shard_path = f"web/shards/{shard_filename}"

                with open(shard_path, 'w', encoding='utf-8') as f:
                    json.dump(shard_data, f, ensure_ascii=False, separators=(',', ':'))

                # 写入后立即验证文件内容
                if shard_key == test_shard_key:
                    try:
                        # 显示文件的绝对路径
                        abs_path = os.path.abspath(shard_path)
                        logger.info(f"文件绝对路径: {abs_path}")

                        with open(shard_path, 'r', encoding='utf-8') as f:
                            written_data = json.load(f)
                            written_wallets = written_data.get("wallets", {})

                        logger.info(f"文件包含钱包总数: {len(written_wallets)}")
                        logger.info(f"前10个钱包地址: {list(written_wallets.keys())[:10]}")

                        if test_address in written_wallets:
                            logger.info(f"✓ 确认地址 {test_address} 已成功写入分片文件")
                            logger.info(f"  写入的数据: {written_wallets[test_address]}")
                        else:
                            logger.error(f"✗ 地址 {test_address} 写入后在文件中未找到")

                            # 检查是否有相似地址
                            similar_addresses = [addr for addr in written_wallets.keys()
                                                 if addr.startswith(test_address[:10])]
                            if similar_addresses:
                                logger.info(f"  找到相似地址: {similar_addresses}")

                            # 检查完整地址列表中是否包含目标地址
                            all_addresses = list(written_wallets.keys())
                            if test_address in all_addresses:
                                logger.info(f"  奇怪：地址在列表中但not in检查失败")
                            else:
                                logger.info(f"  地址确实不在文件中")

                        # 显示文件大小和修改时间
                        file_stat = os.stat(shard_path)
                        logger.info(f"文件大小: {file_stat.st_size} 字节")
                        logger.info(f"文件修改时间: {file_stat.st_mtime}")

                    except Exception as e:
                        logger.error(f"验证写入失败: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

                generated_shards += 1

                # 检查文件大小
                file_size = os.path.getsize(shard_path) / (1024 * 1024)

                # 调试特定分片
                if shard_key == test_shard_key:
                    logger.info(f"已生成分片文件 {shard_filename}:")
                    logger.info(f"  - 文件大小: {file_size:.2f}MB")
                    logger.info(f"  - 钱包数量: {len(valid_wallets)}")
                    # 检查文件内容
                    with open(shard_path, 'r', encoding='utf-8') as f:
                        file_content = json.load(f)
                        if test_address in file_content.get("wallets", {}):
                            logger.info(f"✓ 确认地址 {test_address} 已写入分片文件")
                        else:
                            logger.error(f"✗ 地址 {test_address} 未在分片文件中找到")

            logger.info(f"生成了 {generated_shards} 个分片文件，包含 {total_wallets} 个钱包")
            return True

        except Exception as e:
            logger.error(f"生成分片文件时出错: {e}")
            return False

    def generate_router_index(self) -> bool:
        """生成路由索引文件"""
        logger.info("生成路由索引文件...")

        try:
            # 统计各分片信息
            shard_info = {}
            total_wallets = 0
            total_lb_pairs = 0
            total_positions = 0

            for shard_key, shard_wallets in self.shard_data.items():
                if not shard_wallets:
                    continue

                # 统计有效钱包
                valid_wallets = {
                    wallet: data for wallet, data in shard_wallets.items()
                    if data["newLbPairs"] or data["newPositions"]
                }

                if not valid_wallets:
                    continue

                wallet_count = len(valid_wallets)
                lb_pair_count = sum(len(data["newLbPairs"]) for data in valid_wallets.values())
                position_count = sum(len(data["newPositions"]) for data in valid_wallets.values())

                shard_info[shard_key] = {
                    "filename": f"shard_{shard_key}.json",
                    "walletCount": wallet_count,
                    "lbPairCount": lb_pair_count,
                    "positionCount": position_count
                }

                total_wallets += wallet_count
                total_lb_pairs += lb_pair_count
                total_positions += position_count

            # 生成主路由索引
            router_data = {
                "lastUpdated": self._get_current_timestamp(),
                "shardPrefixLength": self.shard_prefix_length,
                "totalShards": len(shard_info),
                "totalWallets": total_wallets,
                "totalLbPairs": total_lb_pairs,
                "totalPositions": total_positions,
                "shards": shard_info
            }

            with open("web/wallet_index.json", 'w', encoding='utf-8') as f:
                json.dump(router_data, f, ensure_ascii=False, indent=2)

            logger.info(f"路由索引已生成: {len(shard_info)} 个分片，{total_wallets} 个钱包")
            return True

        except Exception as e:
            logger.error(f"生成路由索引时出错: {e}")
            return False

    def generate_stats_file(self) -> bool:
        """生成统计信息文件"""
        logger.info("生成统计信息文件...")

        try:
            # 统计各种数据
            total_wallets = sum(
                len([w for w, data in shard_wallets.items()
                     if data["newLbPairs"] or data["newPositions"]])
                for shard_wallets in self.shard_data.values()
            )

            total_lb_pairs = sum(
                sum(len(data["newLbPairs"]) for data in shard_wallets.values())
                for shard_wallets in self.shard_data.values()
            )

            total_positions = sum(
                sum(len(data["newPositions"]) for data in shard_wallets.values())
                for shard_wallets in self.shard_data.values()
            )

            # 分片分布统计
            shard_distribution = {}
            for shard_key, shard_wallets in self.shard_data.items():
                if shard_wallets:
                    wallet_count = len([w for w, data in shard_wallets.items()
                                        if data["newLbPairs"] or data["newPositions"]])
                    if wallet_count > 0:
                        shard_distribution[shard_key] = wallet_count

            stats = {
                "lastUpdated": self._get_current_timestamp(),
                "summary": {
                    "totalWallets": total_wallets,
                    "totalLbPairs": total_lb_pairs,
                    "totalPositions": total_positions,
                    "totalItems": total_lb_pairs + total_positions,
                    "totalShards": len(shard_distribution)
                },
                "shardDistribution": shard_distribution,
                "shardingConfig": {
                    "prefixLength": self.shard_prefix_length,
                    "maxFileSize": f"{self.max_file_size_mb}MB"
                },
                "version": "1.0.0"
            }

            with open("web/stats.json", 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)

            logger.info("统计信息文件已生成: web/stats.json")
            return True

        except Exception as e:
            logger.error(f"生成统计信息时出错: {e}")
            return False

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.utcnow().isoformat() + "Z"

    def process_all_data(self) -> Dict[str, int]:
        """处理所有数据（分片优化版）"""
        logger.info("开始分片数据处理...")

        results = {
            "lb_pairs_loaded": 0,
            "positions_loaded": 0,
            "shards_generated": 0,
            "files_generated": 0
        }

        try:
            # 加载数据到分片
            results["lb_pairs_loaded"] = self.load_lb_pairs_data()
            results["positions_loaded"] = self.load_positions_data()

            # 生成分片文件
            if self.generate_shard_files():
                results["shards_generated"] = len(self.shard_data)
                results["files_generated"] += results["shards_generated"]

            # 生成路由索引
            if self.generate_router_index():
                results["files_generated"] += 1

            # 生成统计文件
            if self.generate_stats_file():
                results["files_generated"] += 1

            logger.info(f"分片处理完成: {results}")

        except Exception as e:
            logger.error(f"数据处理过程中出错: {e}")

        return results


def main():
    """主函数"""
    try:
        processor = DataProcessor()
        results = processor.process_all_data()

        print("\n" + "=" * 60)
        print("Meteora Fees Checker - 分片数据处理完成")
        print("=" * 60)
        print(f"📊 数据统计:")
        print(f"   新增lbPair: {results['lb_pairs_loaded']} 条")
        print(f"   新增position: {results['positions_loaded']} 条")
        print(f"   生成分片: {results['shards_generated']} 个")
        print(f"   总文件数: {results['files_generated']}")
        print(f"\n🎯 分片策略:")
        print(f"   分片前缀长度: {processor.shard_prefix_length} 字符")
        print(f"   单文件大小限制: {processor.max_file_size_mb} MB")
        print(f"   理论最大分片数: {16 ** processor.shard_prefix_length}")
        print("\n生成的文件:")

        # 列出生成的文件
        web_files = []
        total_size = 0

        # 主文件
        for file in ["wallet_index.json", "stats.json"]:
            file_path = os.path.join("web", file)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                web_files.append(f"  - {file} ({file_size / 1024:.1f} KB)")
                total_size += file_size

        # 分片文件
        shard_dir = "web/shards"
        if os.path.exists(shard_dir):
            shard_count = 0
            for file in os.listdir(shard_dir):
                if file.endswith('.json'):
                    file_path = os.path.join(shard_dir, file)
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    shard_count += 1

            if shard_count > 0:
                web_files.append(f"  - shards/ 目录: {shard_count} 个分片文件")

        if web_files:
            print("\n".join(web_files))
            print(f"\n总文件大小: {total_size / (1024 * 1024):.2f} MB")
        else:
            print("  无文件生成")

        print("\n💡 优势:")
        print("  - 按地址前缀分片，快速定位用户数据")
        print("  - 单文件大小可控，支持长期扩展")
        print("  - 前端按需加载，提升查询性能")
        print("  - 增量处理，避免重复计算")
        print("\n下一步: 部署 web/ 目录到静态托管平台")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())