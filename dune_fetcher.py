#!/usr/bin/env python3
"""
Meteora Fees Checker - 数据拉取脚本
从Dune API获取wallet与lbPair/position的映射关系
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, List, Set
import logging
from dune_client.client import DuneClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DuneFetcher:
    def __init__(self, config_path: str = "config.json"):
        """初始化Dune SDK客户端"""
        self.config = self._load_config(config_path)
        self.api_key = self.config.get("dune_api_key")
        if not self.api_key:
            raise ValueError("Dune API密钥未配置，请在config.json中设置dune_api_key")

        # 初始化Dune SDK客户端
        self.dune = DuneClient(api_key=self.api_key)

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)

        # 记录已处理的数据，避免重复
        self.processed_pairs: Set[str] = set()
        self.processed_positions: Set[str] = set()
        self._load_existing_data()

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_path} 不存在，将创建默认配置")
            default_config = {
                "dune_api_key": "",
                "queries": {
                    "new_lb_pairs": "your_query_id_here",
                    "new_positions": "your_query_id_here"
                }
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return default_config

    def _load_existing_data(self):
        """加载已存在的数据，避免重复拉取"""
        try:
            if os.path.exists("data/new_lb_pairs.jsonl"):
                with open("data/new_lb_pairs.jsonl", 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        key = f"{data['wallet']}_{data['lb_pair']}"
                        self.processed_pairs.add(key)

            if os.path.exists("data/new_positions.jsonl"):
                with open("data/new_positions.jsonl", 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        key = f"{data['wallet']}_{data['position']}"
                        self.processed_positions.add(key)

            logger.info(
                f"已加载 {len(self.processed_pairs)} 个lbPair记录和 {len(self.processed_positions)} 个position记录")
        except Exception as e:
            logger.warning(f"加载现有数据时出错: {e}")

    def execute_query(self, query_id: str) -> List[Dict]:
        """使用Dune SDK执行查询（无参数）"""
        try:
            logger.info(f"使用Dune SDK获取最新查询结果: {query_id}")

            # 直接获取最新缓存的结果
            results = self.dune.get_latest_result(query_id)

            # 提取行数据
            if hasattr(results, 'result') and hasattr(results.result, 'rows'):
                rows = results.result.rows
            elif hasattr(results, 'rows'):
                rows = results.rows
            else:
                logger.warning("无法从结果中提取行数据")
                return []

            logger.info(f"获取到 {len(rows)} 条记录")
            return rows

        except Exception as e:
            logger.error(f"执行查询时出错: {e}")
            return []

    def fetch_new_lb_pairs(self) -> int:
        """获取新的lbPair数据"""
        logger.info("开始获取新lbPair数据...")

        query_id = self.config.get("queries", {}).get("new_lb_pairs")
        if not query_id:
            logger.error("新lbPair查询ID未配置")
            return 0

        # 直接获取查询结果（无参数）
        rows = self.execute_query(query_id)
        if not rows:
            return 0

        new_count = 0
        with open("data/new_lb_pairs.jsonl", 'a', encoding='utf-8') as f:
            for row in rows:
                # 适应实际查询字段：wallet, lbPair
                wallet = row.get("wallet", "").lower()
                lb_pair = row.get("lbPair", "")

                if not wallet or not lb_pair:
                    continue

                key = f"{wallet}_{lb_pair}"
                if key in self.processed_pairs:
                    continue

                record = {
                    "wallet": wallet,
                    "lb_pair": lb_pair,
                    "type": "new_lb_pair",
                    "fetched_at": datetime.utcnow().isoformat()
                }

                f.write(json.dumps(record, ensure_ascii=False) + '\n')
                self.processed_pairs.add(key)
                new_count += 1

        logger.info(f"新增 {new_count} 条lbPair记录")
        return new_count

    def fetch_new_positions(self) -> int:
        """获取新的position数据"""
        logger.info("开始获取新position数据...")

        query_id = self.config.get("queries", {}).get("new_positions")
        if not query_id:
            logger.error("新position查询ID未配置")
            return 0

        # 直接获取查询结果（无参数）
        rows = self.execute_query(query_id)
        if not rows:
            return 0

        new_count = 0
        with open("data/new_positions.jsonl", 'a', encoding='utf-8') as f:
            for row in rows:
                # 适应实际查询字段：wallet, position
                wallet = row.get("wallet", "").lower()
                position = row.get("position", "")

                if not wallet or not position:
                    continue

                key = f"{wallet}_{position}"
                if key in self.processed_positions:
                    continue

                record = {
                    "wallet": wallet,
                    "position": position,
                    "type": "new_position",
                    "fetched_at": datetime.utcnow().isoformat()
                }

                f.write(json.dumps(record, ensure_ascii=False) + '\n')
                self.processed_positions.add(key)
                new_count += 1

        logger.info(f"新增 {new_count} 条position记录")
        return new_count

    def run_full_update(self) -> Dict[str, int]:
        """执行完整的数据更新"""
        logger.info("开始执行完整数据更新...")

        results = {
            "new_lb_pairs": 0,
            "new_positions": 0
        }

        try:
            results["new_lb_pairs"] = self.fetch_new_lb_pairs()
            time.sleep(2)  # 避免API限流

            results["new_positions"] = self.fetch_new_positions()

            logger.info(f"数据更新完成: {results}")

        except Exception as e:
            logger.error(f"数据更新过程中出错: {e}")

        return results


def main():
    """主函数"""
    try:
        fetcher = DuneFetcher()
        results = fetcher.run_full_update()

        print("\n" + "=" * 50)
        print("Meteora Fees Checker - 数据拉取完成")
        print("=" * 50)
        print(f"新lbPair记录: {results['new_lb_pairs']}")
        print(f"新position记录: {results['new_positions']}")
        print(f"总计新增: {sum(results.values())} 条记录")
        print("\n下一步: 运行 python data_processor.py 处理数据")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())