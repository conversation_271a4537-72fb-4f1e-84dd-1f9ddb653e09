#!/usr/bin/env python3
"""
Meteora Fees Checker - 数据拉取脚本
从Dune API获取wallet与lbPair/position的映射关系
"""

import json
import os
import requests
import time
from datetime import datetime
from typing import Dict, List, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DuneFetcher:
    def __init__(self, config_path: str = "config.json"):
        """初始化Dune API客户端"""
        self.config = self._load_config(config_path)
        self.api_key = self.config.get("dune_api_key")
        if not self.api_key:
            raise ValueError("Dune API密钥未配置，请在config.json中设置dune_api_key")

        self.base_url = "https://api.dune.com/api/v1"
        self.headers = {
            "X-Dune-API-Key": self.api_key,
            "Content-Type": "application/json"
        }

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)

        # 记录已处理的数据，避免重复
        self.processed_pairs: Set[str] = set()
        self.processed_positions: Set[str] = set()
        self._load_existing_data()

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_path} 不存在，将创建默认配置")
            default_config = {
                "dune_api_key": "",
                "queries": {
                    "new_lb_pairs": "your_query_id_here",
                    "new_positions": "your_query_id_here"
                }
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return default_config

    def _load_existing_data(self):
        """加载已存在的数据，避免重复拉取"""
        try:
            if os.path.exists("data/new_lb_pairs.jsonl"):
                with open("data/new_lb_pairs.jsonl", 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        key = f"{data['wallet']}_{data['lb_pair']}"
                        self.processed_pairs.add(key)

            if os.path.exists("data/new_positions.jsonl"):
                with open("data/new_positions.jsonl", 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        key = f"{data['wallet']}_{data['position']}"
                        self.processed_positions.add(key)

            logger.info(
                f"已加载 {len(self.processed_pairs)} 个lbPair记录和 {len(self.processed_positions)} 个position记录")
        except Exception as e:
            logger.warning(f"加载现有数据时出错: {e}")

    def execute_query(self, query_id: str, parameters: Dict = None) -> List[Dict]:
        """执行Dune查询"""
        try:
            # 创建查询执行
            execution_payload = {
                "query_id": query_id,
                "parameters": parameters or {}
            }

            response = requests.post(
                f"{self.base_url}/query/{query_id}/execute",
                headers=self.headers,
                json=execution_payload
            )

            if response.status_code != 200:
                logger.error(f"查询执行失败: {response.status_code} - {response.text}")
                return []

            execution_id = response.json()["execution_id"]
            logger.info(f"查询已提交，执行ID: {execution_id}")

            # 等待查询完成
            while True:
                status_response = requests.get(
                    f"{self.base_url}/execution/{execution_id}/status",
                    headers=self.headers
                )

                if status_response.status_code != 200:
                    logger.error(f"状态检查失败: {status_response.status_code}")
                    return []

                status_data = status_response.json()
                state = status_data.get("state")

                if state == "QUERY_STATE_COMPLETED":
                    logger.info("查询执行完成")
                    break
                elif state == "QUERY_STATE_FAILED":
                    logger.error(f"查询执行失败: {status_data}")
                    return []
                else:
                    logger.info(f"查询状态: {state}，等待中...")
                    time.sleep(5)

            # 获取查询结果
            results_response = requests.get(
                f"{self.base_url}/execution/{execution_id}/results",
                headers=self.headers
            )

            if results_response.status_code != 200:
                logger.error(f"结果获取失败: {results_response.status_code}")
                return []

            results = results_response.json()
            rows = results.get("result", {}).get("rows", [])
            logger.info(f"获取到 {len(rows)} 条记录")

            return rows

        except Exception as e:
            logger.error(f"执行查询时出错: {e}")
            return []

    def fetch_new_lb_pairs(self) -> int:
        """获取新的lbPair数据"""
        logger.info("开始获取新lbPair数据...")

        query_id = self.config.get("queries", {}).get("new_lb_pairs")
        if not query_id:
            logger.error("新lbPair查询ID未配置")
            return 0

        # 查询参数：只获取2025-07-01之后的数据
        parameters = {
            "start_date": "2025-07-01 00:00:00"
        }

        rows = self.execute_query(query_id, parameters)
        if not rows:
            return 0

        new_count = 0
        with open("data/new_lb_pairs.jsonl", 'a', encoding='utf-8') as f:
            for row in rows:
                wallet = row.get("wallet_address", "").lower()
                lb_pair = row.get("lb_pair_address", "")
                created_at = row.get("created_at", "")

                if not wallet or not lb_pair:
                    continue

                key = f"{wallet}_{lb_pair}"
                if key in self.processed_pairs:
                    continue

                record = {
                    "wallet": wallet,
                    "lb_pair": lb_pair,
                    "created_at": created_at,
                    "type": "new_lb_pair",
                    "fetched_at": datetime.utcnow().isoformat()
                }

                f.write(json.dumps(record, ensure_ascii=False) + '\n')
                self.processed_pairs.add(key)
                new_count += 1

        logger.info(f"新增 {new_count} 条lbPair记录")
        return new_count

    def fetch_new_positions(self) -> int:
        """获取新的position数据"""
        logger.info("开始获取新position数据...")

        query_id = self.config.get("queries", {}).get("new_positions")
        if not query_id:
            logger.error("新position查询ID未配置")
            return 0

        # 查询参数：只获取2025-07-01之后的数据
        parameters = {
            "start_date": "2025-07-01 00:00:00"
        }

        rows = self.execute_query(query_id, parameters)
        if not rows:
            return 0

        new_count = 0
        with open("data/new_positions.jsonl", 'a', encoding='utf-8') as f:
            for row in rows:
                wallet = row.get("wallet_address", "").lower()
                position = row.get("position_id", "")
                lb_pair = row.get("lb_pair_address", "")
                created_at = row.get("created_at", "")

                if not wallet or not position:
                    continue

                key = f"{wallet}_{position}"
                if key in self.processed_positions:
                    continue

                record = {
                    "wallet": wallet,
                    "position": position,
                    "lb_pair": lb_pair,
                    "created_at": created_at,
                    "type": "new_position",
                    "fetched_at": datetime.utcnow().isoformat()
                }

                f.write(json.dumps(record, ensure_ascii=False) + '\n')
                self.processed_positions.add(key)
                new_count += 1

        logger.info(f"新增 {new_count} 条position记录")
        return new_count

    def run_full_update(self) -> Dict[str, int]:
        """执行完整的数据更新"""
        logger.info("开始执行完整数据更新...")

        results = {
            "new_lb_pairs": 0,
            "new_positions": 0
        }

        try:
            results["new_lb_pairs"] = self.fetch_new_lb_pairs()
            time.sleep(2)  # 避免API限流

            results["new_positions"] = self.fetch_new_positions()

            logger.info(f"数据更新完成: {results}")

        except Exception as e:
            logger.error(f"数据更新过程中出错: {e}")

        return results


def main():
    """主函数"""
    try:
        fetcher = DuneFetcher()
        results = fetcher.run_full_update()

        print("\n" + "=" * 50)
        print("Meteora Fees Checker - 数据拉取完成")
        print("=" * 50)
        print(f"新lbPair记录: {results['new_lb_pairs']}")
        print(f"新position记录: {results['new_positions']}")
        print(f"总计新增: {sum(results.values())} 条记录")
        print("\n下一步: 运行 python data_processor.py 处理数据")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
