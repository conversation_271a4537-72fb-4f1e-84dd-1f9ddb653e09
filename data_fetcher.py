#!/usr/bin/env python3
"""
Meteora Fees Checker - 数据获取脚本
从 Dune 获取 wallet 与 lbPair/position 的对应关系
"""

import json
import logging
import os
import time
from datetime import datetime, timezone
from typing import Dict, List

from dotenv import load_dotenv
from dune_client.client import DuneClient

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MeteoraDataFetcher:
    def __init__(self):
        """初始化数据获取器"""
        self.wallet_lbpair_query_id = os.getenv("wallet_lbpair_query_id")
        self.wallet_position_query_id = os.getenv("wallet_position_query_id")
        self.dune_api_key = os.getenv("DUNE_API_KEY")
        self.dune = DuneClient(self.dune_api_key)
        self.data_dir = "data"
        self.ensure_data_dir()

        # 数据文件路径
        self.wallet_lbpair_file = os.path.join(self.data_dir, "wallet_lbpair.json")
        self.wallet_position_file = os.path.join(self.data_dir, "wallet_position.json")
        self.position_lbpair_file = os.path.join(self.data_dir, "position_lbpair.json")
        self.metadata_file = os.path.join(self.data_dir, "metadata.json")

    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def load_existing_data(self, file_path: str) -> Dict:
        """加载现有数据"""
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"无法加载 {file_path}: {e}")
                return {}
        return {}

    def save_data(self, data: Dict, file_path: str):
        """保存数据到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
            logger.info(f"数据已保存到 {file_path}")
        except Exception as e:
            logger.error(f"保存数据失败 {file_path}: {e}")

    def fetch_wallet_lbpair_data(self, query_id: str) -> List[Dict]:
        """从 Dune 获取 wallet 和 lbPair 的对应关系"""
        try:
            logger.info(f"正在获取 wallet-lbPair 数据，Query ID: {query_id}")
            result = self.dune.get_latest_result(query_id)

            if result and hasattr(result, 'result') and result.result:
                return result.result.rows
            return []
        except Exception as e:
            logger.error(f"获取 wallet-lbPair 数据失败: {e}")
            return []

    def fetch_wallet_position_data(self, query_id: str) -> List[Dict]:
        """从 Dune 获取 wallet 和 position 的对应关系"""
        try:
            logger.info(f"正在获取 wallet-position 数据，Query ID: {query_id}")
            result = self.dune.get_latest_result(query_id)

            if result and hasattr(result, 'result') and result.result:
                return result.result.rows
            return []
        except Exception as e:
            logger.error(f"获取 wallet-position 数据失败: {e}")
            return []

    def update_wallet_lbpair_mapping(self, new_data: List[Dict]):
        """更新 wallet 和 lbPair 的映射关系"""
        existing_data = self.load_existing_data(self.wallet_lbpair_file)

        for row in new_data:
            wallet = row.get('wallet', '').strip()
            lbpair = row.get('lbPair', '').strip()

            if wallet and lbpair:
                if wallet not in existing_data:
                    existing_data[wallet] = set()
                else:
                    # 转换为 set 以便添加
                    existing_data[wallet] = set(existing_data[wallet])

                existing_data[wallet].add(lbpair)

        # 转换 set 回 list 以便 JSON 序列化
        for wallet in existing_data:
            if isinstance(existing_data[wallet], set):
                existing_data[wallet] = list(existing_data[wallet])

        self.save_data(existing_data, self.wallet_lbpair_file)
        logger.info(f"更新了 {len(existing_data)} 个钱包的 lbPair 映射")

    def update_wallet_position_mapping(self, new_data: List[Dict]):
        """更新 wallet 和 position 的映射关系"""
        existing_wallet_position = self.load_existing_data(self.wallet_position_file)
        existing_position_lbpair = self.load_existing_data(self.position_lbpair_file)

        for row in new_data:
            wallet = row.get('wallet', '').strip()
            position = row.get('position', '').strip()
            lbpair = row.get('lbPair', '').strip()

            if wallet and position:
                # 更新 wallet -> position 映射
                if wallet not in existing_wallet_position:
                    existing_wallet_position[wallet] = set()
                else:
                    existing_wallet_position[wallet] = set(existing_wallet_position[wallet])

                existing_wallet_position[wallet].add(position)

                # 更新 position -> lbPair 映射
                if lbpair:
                    existing_position_lbpair[position] = lbpair

        # 转换 set 回 list
        for wallet in existing_wallet_position:
            if isinstance(existing_wallet_position[wallet], set):
                existing_wallet_position[wallet] = list(existing_wallet_position[wallet])

        self.save_data(existing_wallet_position, self.wallet_position_file)
        self.save_data(existing_position_lbpair, self.position_lbpair_file)

        logger.info(f"更新了 {len(existing_wallet_position)} 个钱包的 position 映射")
        logger.info(f"更新了 {len(existing_position_lbpair)} 个 position-lbPair 映射")

    def update_metadata(self):
        """更新元数据信息"""
        metadata = self.load_existing_data(self.metadata_file)
        metadata['last_updated'] = datetime.now(timezone.utc).isoformat()
        metadata['season2_start'] = '2025-07-01T00:00:00Z'

        # 计算数据统计
        wallet_lbpair_data = self.load_existing_data(self.wallet_lbpair_file)
        wallet_position_data = self.load_existing_data(self.wallet_position_file)

        metadata['stats'] = {
            'total_wallets_with_lbpair': len(wallet_lbpair_data),
            'total_wallets_with_position': len(wallet_position_data),
            'total_unique_wallets': len(set(list(wallet_lbpair_data.keys()) + list(wallet_position_data.keys())))
        }

        self.save_data(metadata, self.metadata_file)

    def run_daily_update(self):
        """执行每日数据更新"""
        logger.info("开始每日数据更新...")

        # 获取 wallet-lbPair 数据
        wallet_lbpair_data = self.fetch_wallet_lbpair_data(self.wallet_lbpair_query_id)
        if wallet_lbpair_data:
            self.update_wallet_lbpair_mapping(wallet_lbpair_data)

        # 等待一下避免API限制
        time.sleep(2)

        # 获取 wallet-position 数据
        wallet_position_data = self.fetch_wallet_position_data(self.wallet_position_query_id)
        if wallet_position_data:
            self.update_wallet_position_mapping(wallet_position_data)

        # 更新元数据
        self.update_metadata()

        logger.info("每日数据更新完成!")


def main():
    fetcher = MeteoraDataFetcher()
    fetcher.run_daily_update()


if __name__ == "__main__":
    main()
